<?php

namespace Nzoom\Mvc\ControllerTrait;

use Nzoom\Export\ExportService;
use Nzoom\Export\ExportActionFactory;

/**
 * Trait for handling grid export actions
 *
 * @method getViewer(): Viewer
 * @property \Registry $registry
 * @property string $module
 * @property string $controller
 * @property string $modelName
 * @property string $modelFactoryName
 */
trait GridExportActionTrait
{
    public function createExportAction(string $module_check, array $types, array $typeSections): ?array
    {
        if (
            ! $this->checkActionPermissions($module_check, 'export')
            || in_array($module_check, $this->systemExportModules)
        ) {
            return null;
        }

        // Create factory and delegate to it
        $factory = new ExportActionFactory(
            $this->registry,
            $this->module,
            $this->controller,
            $this->modelName,
            $this->modelFactoryName,
            $this->registry['translater']
        );

        return $factory->createExportAction($module_check, $types, $typeSections);
    }

    public function _export2()
    {
        $registry = $this->registry;
        /** @var \Request $request */
        $request = $registry['request'];

        $exportEmpty = $request->get('export_what');
        // Get export format and items from request
        $format = $request->get('format');
        $items = $request->getPost('items', []);
        $filename = $request->get('file_name');
        $type_id = $request->get('type_id');
        $type_section_id = $request->get('type_section_id');


        // Validate items
        if (!$exportEmpty && (empty($items) || !is_array($items))) {
            $this->_handleExportError('No items selected for export');
            return;
        }

        $user_id = (int)$registry['currentUser']->get('id');
        $role_id = (int)$registry['currentUser']->get('role');

        $outlook = \Outlooks::getOutlook(
            $registry,
            !empty($type_section_id),
            (int) ($type_id ?? $type_section_id ?? 0),
            $user_id,
            $role_id,
            $this->module,
            $this->controller
        );

        // Check if we need to get tags
        $modelFields = [];
        foreach ($outlook->get('current_custom_fields') as $v) {
            if (!$v['position']) {
                // no position means not visible
                continue;
            }
            $modelFields[$v['name']] = $v;
        }

        $modelFieldsNames = array_column($modelFields, 'name');
        if (in_array('tags', $modelFieldsNames)) {
            $this->registry->set('getTags', true, true);
        }

        // Generate filename if empty
        if (empty($filename)) {
            $filename = strtolower($this->module . '_' . $this->controller . '_export_' . date('Y-m-d_H-i-s'));
        }

        // Create export service and perform export
        try {
            $exportService = new ExportService($this->registry, $this->module, $this->controller, $format);

            $modelType = $this->modelFactoryName;
            $isExcel = in_array(strtolower($format), ['xls', 'xlsx']) ;
            if ($isExcel && $exportEmpty === 'empty') {
                // Create empty export data with headers only using DataFactory
                $dataFactory = new \Nzoom\Export\DataFactory($this->registry);

                $data = $dataFactory->createEmpty($modelType::$modelName, $outlook);
            } else {
                $filters = $this->createExportItemsFilter($items, $outlook);
                $factoryClass = $this->getFactoryClass();
                $groupTables = $request->getPost('group_tables', false);
                if ($isExcel && $groupTables) {
                    $data = $exportService->createExportDataWithTables($outlook, $filters, $factoryClass);
                } else {
                    $data = $exportService->createExportData($outlook, $filters, $factoryClass);
                }
            }
           //var_dump($data); exit;
            // Get export options for constrained sizing (Excel only)
            $exportOptions = $this->getExportOptions($request, $format);
            $exportService->export($filename, $data, $exportOptions);
        } catch (\Exception $e) {
            $this->_handleExportError('Export failed: ' . $e->getMessage());
        }
        exit;
    }

    /**
     * Get export options from request for constrained sizing
     *
     * @param \Request $request
     * @param string $format
     * @return array
     */
    protected function getExportOptions(\Request $request, string $format): array
    {
        $options = [];

        // Only apply sizing constraints for Excel formats
        if (in_array(strtolower($format), ['xls', 'xlsx'])) {
            // Get sizing options from request (with sensible defaults)
            $maxColumnWidth = $request->getPost('max_column_width', 50.0);
            $maxRowHeight = $request->getPost('max_row_height', 100.0);
            $chunkSize = $request->getPost('chunk_size', 1000);
            $groupTables = $request->getPost('group_tables', false);
            $includeEnumeration = $request->getPost('include_enumeration', true);


            // Validate and sanitize the values
            $options['max_column_width'] = max(10.0, min(255.0, (float) $maxColumnWidth));
            $options['max_row_height'] = max(15.0, min(500.0, (float) $maxRowHeight));
            $options['chunk_size'] = max(100, min(5000, (int) $chunkSize));
            $options['group_tables'] =  $groupTables;
            $options['include_enumeration'] = (bool) $includeEnumeration;

        }

        if (in_array(strtolower($format), ['csv'])) {
            // Adapter default is ISO Y-m-d however, legacy behaviour is d.m.Y,
            // so we add support for options that can control it but assume legacy as default.
            $dateFormat = $request->getPost('date_format', 'd.m.Y');
            $dateTimeFormat = $request->getPost('datetime_format', 'd.m.Y H:i:s');
            $options['date_format'] = $dateFormat;
            $options['datetime_format'] = $dateTimeFormat;
        }

        return $options;
    }

    /**
     * Handle export error
     *
     * @param string $message Error message
     * @param int $statusCode HTTP status code to use (default: 400)
     * @return void
     */
    protected function _handleExportError($message, $statusCode = 400)
    {
        // Log the error if logger is available
        if (isset($this->registry['logger'])) {
            $this->registry['logger']->error('Export error: ' . $message);
        }

        // Set error message in registry for AJAX response
        $this->registry->set('ajax_result', json_encode([
            'error' => $message,
            'status' => 'error',
            'timestamp' => date('Y-m-d H:i:s')
        ]), true);

        // If this is an AJAX request, send appropriate headers
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {

            http_response_code($statusCode);
            header('Content-Type: application/json');
            echo $this->registry->get('ajax_result');
            exit;
        }

        // For non-AJAX requests, we'll let the controller handle the response
    }

    /**
     * @param array $items
     * @param $alias
     * @param \Outlook $outlook
     * @return array
     */
    private function createExportItemsFilter(array $items, \Outlook $outlook): array
    {
        $registry = $this->registry;
        $modelFactoryName = $this->modelFactoryName;
        $alias = $modelFactoryName::getAlias($registry['module'], $registry['controller'], $registry['action']);


        $items = array_map('intval', $items);
        $idsList = implode(',', $items);
        $where = [
            "{$alias}.id IN ($idsList)"
        ];
        $filters = [
            'where' => $where,
            'get_fields' => $outlook->get('current_custom_fields'),
        ];
        return $filters;
    }

    /**
     * @return mixed|string
     * @throws \Exception
     */
    private function getFactoryClass()
    {
        $factoryClass = $this->modelFactoryName;
        if (strpos($factoryClass, '\\') === false && class_exists("\\" . $factoryClass)) {
            $factoryClass = "\\" . $factoryClass;
        }

        // Validate factory class has search method
        if (!method_exists($factoryClass, 'search')) {
            throw new \Exception("Factory class $factoryClass does not have a search method");
        }
        return $factoryClass;
    }
}
