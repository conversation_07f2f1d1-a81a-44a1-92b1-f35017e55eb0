<?php

class Tags_Sections_Edit_Viewer extends Viewer {
    public $template = 'sections_edit.html';

    public function prepare() {
        $this->model = $this->registry['tags_section'];

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['controller_param'], $this->controller,
                            $this->registry['action_param'], $this->action,
                            $this->action, $this->model->get('id'));
        $this->data['submitLink'] = $this->submitLink;

        // current checked tags for tag section
        $tag_id = $this->model->get('tag_id') ?: array();

        $params = array(
            0 => $this->registry,
            'table' => 'DB_TABLE_TAGS',
            'table_i18n' => 'DB_TABLE_TAGS_I18N',
            'label' => 'name',
            'value' => 'id, \'^\', model, \'^\', section',
            'order_by' => 'model ASC'
        );
        $tags_data = Dropdown::getCustomDropdown($params);
        $tags_optgroups = array();
        $tags_optgroups_checked = array();
        foreach ($tags_data as $opt) {
            list($opt['option_value'], $module_controller, $section) = explode('^', $opt['option_value']);
            $label_module_controller = $this->i18n('tags_model_' . $module_controller);
            if (!isset($tags_optgroups[$label_module_controller])) {
                $tags_optgroups[$label_module_controller] = array();
            }
            // tag belongs to another group
            if ($section && $section != $this->model->get('id')) {
                $opt['class_name'] = 'oblique';
            }
            // collect which optgroups have checked tags
            if (in_array($opt['option_value'], $tag_id) && !in_array($label_module_controller, $tags_optgroups_checked)) {
                $tags_optgroups_checked[] = $label_module_controller;
            }
            $tags_optgroups[$label_module_controller][] = $opt;
        }
        ksort($tags_optgroups);
        $this->data['tags_optgroups'] = $tags_optgroups;

        // first tab with checked tags should be selected
        if ($tags_optgroups_checked) {
            foreach ($tags_optgroups as $og_label => $og) {
                if (in_array($og_label, $tags_optgroups_checked)) {
                    $this->data['tags_optgroups_selected_tab'] = $og_label;
                    break;
                }
            }
        }

        //prepare group tree
        require_once(PH_MODULES_DIR . 'groups/models/groups.factory.php');
        $this->data['groups'] = Groups::getTree($this->registry);

        $this->prepareTranslations();

        $this->prepareTitleBar();
    }

    public function prepareTitleBar() {
        $title = $this->i18n('tags_sections_edit');
        $this->data['title'] = $title;
    }
}

?>
