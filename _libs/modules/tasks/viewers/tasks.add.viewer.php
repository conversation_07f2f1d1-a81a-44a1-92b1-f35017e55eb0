<?php

class Tasks_Add_Viewer extends Viewer {
    public $template = 'add.html';

    public function prepare() {

        //get counter for the task and sanitize it
        $this->model->getCounter();
        $this->model->counter->sanitize();

        // prepare layout index
        $this->prepareLayoutIndex();

        //set submit link
        $this->submitLink = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s',
                            $_SERVER['PHP_SELF'],
                            $this->registry['module_param'], $this->module,
                            $this->registry['action_param'], $this->action,
                            'type', $this->model->get('type'));
        $this->data['submitLink'] = $this->submitLink;

        if ($this->registry['action'] == 'ajax_add') {
            if ($this->model->get('allowed_types')) {
                 $this->data['submitLink'] .= '&amp;allowed_types=' . $this->model->get('allowed_types');
            }

            $this->data['tasks_types'] = $this->registry['tasks_types'];

            if ($this->registry['currentUser']->checkRights($this->module, 'assign') && $this->model->checkPermissions('assign')) {

                // types of assignments to display
                $a_types = array_intersect(
                    array('owner', 'responsible', 'observer', 'decision'),
                    $this->registry['config']->getParamAsArray(
                        $this->module,
                        'assignment_types_' . $this->model->get('type')
                    )
                );

                if ($a_types) {
                    $a_layouts = array();
                    foreach ($a_types as $a_type) {
                        $a_layouts['assignments_' . $a_type] = array(
                            'name' => $this->i18n($this->module . '_assign_' . $a_type)
                        ) +
                        $this->model->getLayoutsDetails('assignments_' . $a_type);

                        // user ids as keys are needed
                        if ($this->model->get('assignments_' . $a_type)) {
                            $this->model->set('assignments_' . $a_type, array_flip($this->model->get('assignments_' . $a_type)), true);
                        }
                    }

                    $layouts_details = $this->model->get('layouts_details');
                    General::injectInArray($a_layouts, $layouts_details, 'after', 'department');
                    $this->model->set('layouts_details', $layouts_details, true);

                    $this->data['assignments_settings'] = $this->model->getAssignmentsSettings();

                    $this->loadCustomI18NFiles(PH_MODULES_DIR . 'assignments/i18n/' . $this->registry['lang'] . '/assignments.ini');
                }
            }
        }

        //prepare task type
        require_once($this->modelsDir . 'tasks.types.factory.php');
        $filters = array('sanitize' => true,
                         'model_lang' => $this->model->get('model_lang'),
                         'where' => array('tt.id = ' . $this->model->get('type')));
        $this->data['task_type'] = Tasks_Types::searchOne($this->registry, $filters);

        if ($this->data['task_type']) {
            $this->model->set('type_name', $this->data['task_type']->get('name'), true);
        }

        // GET DEPARTMENTS
        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
        $departments = Departments::getTree($this->registry);
        $this->data['departments'] = $departments;

        // GET GROUPS
        require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
        $groups = Groups::getTree($this->registry);
        $this->data['groups'] = $groups;

        if ($this->registry['currentUser']->get('is_portal')) {
            $currentUser = &$this->registry['currentUser'];
            if ($currentUser->get('default_customer')) {
                $this->model->set('customer', $currentUser->get('default_customer'), true);
                $this->model->set('branch', $currentUser->get('default_branch'), true);
                $this->model->set('contact_person', $currentUser->get('default_person'), true);
                $this->model->set('trademark', $currentUser->get('default_trademark'), true);
            }
            if ($currentUser->get('default_project')) {
                $this->model->set('project', $currentUser->get('default_project'), true);
                $this->model->set('phase', $currentUser->get('default_phase'), true);
            }
        }

        //prepare customer
        $customer_id = $this->model->get('customer');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('c.id = ' . $customer_id),
                             'model_lang' => $this->model->get('model_lang'));
            $customer = Customers::searchOne($this->registry, $filters);
            if ($customer) {
                $customer_name = $customer->get('name');
                if (!$customer->get('is_company')) {
                    $customer_name .= ' ' . $customer->get('lastname');
                }
                $this->model->set('customer_code', $customer->get('code'), true);
                $this->model->set('customer_name', $customer_name, true);
                $this->model->set('customer', $customer_id, true);
                $this->model->set('customer_is_company', $customer->get('is_company'), true);
                if (!$this->model->get('trademark')) {
                    $this->model->set('trademark', $customer->get('main_trademark'), true);
                }
                if (!$this->model->get('branch')) {
                    $this->model->set('branch', $customer->get('main_branch_id'), true);
                }
                if (!$this->model->get('contact_person')) {
                    $this->model->set('contact_person', $customer->get('contact_person_id'), true);
                }

                if ($customer->get('is_company')) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                    $filters_branches = array('sanitize' => true,
                                              'model_lang' => $this->model->get('model_lang'),
                                              'where' => array('c.parent_customer = ' . $customer_id,
                                                               'c.subtype = \'branch\'',
                                                               'c.active = 1'),
                                              'sort' => array('c.is_main DESC', 'ci18n.name ASC', 'c.id DESC'));
                    $this->data['customer_branches'] = Customers_Branches::search($this->registry, $filters_branches);
                    if (count($this->data['customer_branches']) && !$this->model->get('branch')) {
                        $this->model->set('branch', $this->data['customer_branches'][0]->get('id'), true);
                    }

                    if ($this->model->get('branch')) {
                        foreach ($this->data['customer_branches'] as $customer_branch) {
                            if ($customer_branch->get('id') == $this->model->get('branch')) {
                                $this->model->set('branch_name', $customer_branch->get('name'), true);
                                $this->model->set('branch_active', $customer_branch->get('active'), true);
                                break;
                            }
                        }

                        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                        $filters_contacts = array('sanitize' => true,
                                                  'model_lang' => $this->model->get('model_lang'),
                                                  'where' => array('c.parent_customer = ' . $this->model->get('branch'),
                                                                   'c.subtype = \'contact\'',
                                                                   'c.active = 1'),
                                                  'sort' => array('c.is_main DESC', 'CONCAT_WS(\' \', ci18n.name, ci18n.lastname) ASC', 'c.id DESC'));
                        $this->data['contact_persons'] = Customers_Contactpersons::search($this->registry, $filters_contacts);
                        if ($this->model->get('contact_person')) {
                            foreach ($this->data['contact_persons'] as $customer_contact_person) {
                                if ($customer_contact_person->get('id') == $this->model->get('contact_person')) {
                                    $this->model->set('contact_person_name', trim($customer_contact_person->get('name') . ' ' . $customer_contact_person->get('lastname')), true);
                                    $this->model->set('contact_person_active', $customer_contact_person->get('active'), true);
                                    break;
                                }
                            }
                        }
                    }
                }
            } else {
                $customer_id = 0;
            }
        }
        if (!$customer_id) {
            // clear all related properties
            $this->model->set('customer_code', '', true);
            $this->model->set('customer_name', '', true);
            $this->model->set('customer', '', true);
            $this->model->set('customer_is_company', '', true);
            $this->model->set('branch', '', true);
            $this->model->set('branch_name', '', true);
            $this->model->set('contact_person', '', true);
            $this->model->set('contact_person_name', '', true);
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare trademark
        $trademark_id = $this->model->get('trademark');
        if ($trademark_id) {
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('n.deleted IS NOT NULL',
                                              'n.id = ' . $trademark_id));
            //flag to search in customers trademarks
            $filters['session_param'] = 'filter_trademark_nomenclature';
            $nomenclature = Nomenclatures::searchOne($this->registry, $filters);
            if ($nomenclature) {
                $this->model->set('trademark', $trademark_id, true);
                $this->model->set('trademark_name', $nomenclature->get('name'), true);
            } else {
                $trademark_id = 0;
            }
        }
        if (!$trademark_id) {
            // clear all related properties
            $this->model->set('trademark', '', true);
            $this->model->set('trademark_name', '', true);
        }

        //prepare project
        $project_id = $this->model->get('project');
        if ($project_id) {
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $this->model->get('model_lang'),
                             'where' => array('p.id = ' . $project_id));
            $project = Projects::searchOne($this->registry, $filters);
            $phases = array();
            if ($project) {
                $this->model->set('project_code', $project->get('code'), true);
                $this->model->set('project_name', $project->get('name'), true);

                if ($project->get('stages')) {
                    $project->getIncludedStages();
                    foreach($project->get('stages_info') as $status => $stages_status) {
                        foreach ($stages_status as $stg_id => $stage) {
                            $phases[] = array(
                                'label'        => $stage->get('name'),
                                'option_value' => $stage->get('id'));

                            if ($stage->get('id') == $this->model->get('phase')) {
                                $this->model->set('phase_name', $stage->get('name'), true);
                            }
                        }
                    }
                }
                if (!$this->model->get('phase')) {
                    $this->model->set('phase', $project->get('stage'), true);
                    $this->model->set('phase_name', $project->get('stage_name'), true);
                }
            } else {
                $project_id = 0;
            }
            $this->data['phases'] = $phases;
        }
        if (!$project_id) {
            // clear all related properties
            $this->model->set('project_code', '', true);
            $this->model->set('project_name', '', true);
            $this->model->set('project', '', true);
            $this->model->set('phase', '', true);
            $this->model->set('phase_name', '', true);
        }

        require_once PH_MODULES_DIR . 'tasks/models/tasks.dropdown.php';
        $this->data['severitys'] = Tasks_Dropdown::getSeverity(array($this->registry));

        //prepare tasks configurators
        require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
        $config_task_patterns = Tasks_Configurators::search($this->registry,
                                array('where' => array('tc.model_type = \'' . $this->model->get('type') . '\'',
                                                       'tc.user_id IN (0, ' . $this->registry['currentUser']->get('id') . ')',
                                                       'tc.lang = \'' . $this->model->get('model_lang') . '\'')));
        $config_task_patterns_options = array();
        $all = $this->i18n('all');
        $mine = $this->i18n('mine');
        foreach ($config_task_patterns as $configurator) {
            if ($configurator->get('user_id')) {
                $optgroup = $mine;
            } else {
                $optgroup = $all;
            }
            $config_task_patterns_options[$optgroup][] = array(
            'label' => $configurator->get('name'),
            'option_value' => $configurator->get('id'));
        }
        $this->data['configTaskPatterns'] = $config_task_patterns_options;

        //if the task has not number and the counter requires project code, then make the project required field
        $this->data['project_required'] = ($this->model->counter->get('project_code')) ? 1 : 0;

        // get additional required fields
        $fields = $this->registry['config']->getParamAsArray($this->module, 'validate_' . $this->model->get('type'));
        $this->data['required_fields'] = array_filter($fields, function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; });

        //remove the counter from the task
        unset($this->model->counter);

        if ($this->theme->isModern()) {
            $this->data['dont_wrap_content'] = true;
            $this->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        }
        $this->prepareTitleBar();

        if ($this->registry['action'] == 'ajax_add' || $this->registry['request']->get('reload')) {
            echo 'var result = ' . json_encode(array('data' => $this->fetch(), 'title' => $this->data['title'])) . ';';
            exit;
        }
    }

    public function prepareTitleBar() {
        // TODO: find a consensus for the title!
        if ($this->theme->isModern()) {
            $ident = $this->model->getIdentifierStr();
            $descr = $this->model->getRecordDescriptionStr();
            if (!empty($descr)) {
                $title = "$descr $ident";
            } else {
                $title = $ident;
            }
        } else {
            $title = sprintf($this->i18n('tasks_add_new'), $this->model->getModelTypeName());
        }
        $this->data['title'] = $title;
    }
}

?>
