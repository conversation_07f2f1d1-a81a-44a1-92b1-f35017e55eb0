<?php
/**
 * CHECK IN plugin custom model class
 */
Class Custom_Model extends Model {
    public $modelName = 'Dashlet';
    public $plugin_name = 'multiple_check_in';
    public $type_id = '';

    public function __construct($dashlet_settings = '') {
        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        return true;
    }

    public function prepareCustomData(&$model) {
        //prepare custom filters
        $filters = array();
        if ($model->get('title_template')) {
            $filters['title_template'] = $model->get('title_template');
            $model->unsetProperty('title_template', true);
        }

        if($model->get('employees')) {
            $filters['employees'] = $model->get('employees');
            $model->unsetProperty('employees', true);
        }

        $model->set('filters', $filters, true);

        return $model;
    }

    /*
     * Function to parse the settings for the current plugin and
     * to set them as properties to the model
     */
    public function parseDashletPluginSettings($dashlet_settings) {
        $settings_rows = preg_split('/(\n|\r|\r\n)/', $dashlet_settings);
        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0]=='#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);
            $this->$key = $value;
        }
    }

    /*
     * Finds the properties for all the required employees
     */
    public function getRequiredEmployees ($dashlet_employees = array()) {
        $employees_data = array();
        if (! empty($dashlet_employees)) {
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $filters_employees = array('model_lang'    => $this->registry['lang'],
                                       'sanitize'      => true,
                                       'sort'  => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                       'where' => array('u.active = 1',
                                                        'u.employee IN (' . implode(',', $dashlet_employees) . ')'
                                                       )
                                       );
            $employees = Users::search($this->registry, $filters_employees);
            foreach($employees as $employee) {
                $employees_data[$employee->get('employee')] = array(
                    'id'        => $employee->get('id'),
                    'name'      => $employee->get('firstname') . ' ' . $employee->get('lastname'),
                    'employee'  => $employee->get('employee'),
                    'arrive'    => false,
                    'leave'     => false
                );
            }
        }

        $this->set('employees_data', $employees_data, true);

        return $employees_data;
    }

    // function to check the current status of the employees
    public function checkCurrentStatus() {
        $current_user_employees = $this->get('employees_data');

        if (!empty($current_user_employees)) {
            $employee_ids = array_keys($current_user_employees);

            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            $filters = array('where'        => array('d.type="' . $this->type_id . '"',
                                                     'DATE_FORMAT(d.added, "%Y-%m-%d")="' . date("Y-m-d") . '"'
                                                    ),
                             'model_lang'   => $this->registry['lang']);
            $current_document = Documents::searchOne($this->registry, $filters);
            if ($current_document) {
                $current_document->getVars();

                $group_vars_per_employee = array();
                $full_group_vars_info_per_employee = array();

                foreach($current_document->get('vars') as $key => $group_var) {
                    if ($group_var['grouping']) {
                        if (! in_array($group_var['grouping'], $group_vars_per_employee)) {
                            if ($group_var['grouping'] && $group_var['type']!='group' && !empty($group_var['source']) && !empty($group_var['options'])) {
                                foreach ($group_var['options'] as $option) {
                                    if (in_array($option['option_value'], $employee_ids)) {
                                        $current_user_employees[$option['option_value']]['user_var'] = $group_var['id'];
                                        $group_vars_per_employee[$option['option_value']] = $group_var['grouping'];
                                        $full_group_vars_info_per_employee[$option['option_value']]['user_var'] = $group_var;
                                    }
                                }
                            }
                        } else if ($group_var['type']=='time') {
                            $group_vars_arr = array_keys($group_vars_per_employee, $group_var['grouping']);
                            if (! empty ($group_vars_arr)) {
                                foreach ($group_vars_arr as $g_arr) {
                                    if (! isset($current_user_employees[$g_arr]['start_var'])) {
                                        $current_user_employees[$g_arr]['start_var'] = $group_var['id'];
                                        $full_group_vars_info_per_employee[$g_arr]['start_var'] = $group_var;
                                    } else {
                                        $current_user_employees[$g_arr]['finish_var'] = $group_var['id'];
                                        $full_group_vars_info_per_employee[$g_arr]['finish_var'] = $group_var;
                                    }
                                }
                            }
                        }
                    }
                }

                foreach ($full_group_vars_info_per_employee as $key_fi => $fgvi) {
                    $user_row = 0;

                    if (isset($fgvi['user_var'])) {
                        if (is_array($fgvi['user_var']['value']) && in_array($key_fi, $fgvi['user_var']['value'])) {
                            $user_row = array_search($key_fi, $fgvi['user_var']['value']);
                        }
                    }

                    if (isset($fgvi['start_var']) && $user_row) {
                        if (!empty($fgvi['start_var']['value'][$user_row])) {
                            $current_user_employees[$key_fi]['arrive'] = true;
                        }
                    }

                    if (isset($fgvi['finish_var']) && $user_row) {
                        if (!empty($fgvi['finish_var']['value'][$user_row])) {
                            $current_user_employees[$key_fi]['leave'] = true;
                        }
                    }
                }
            }
        }

        $this->set('employees_data', $current_user_employees, true);

        return $current_user_employees;
    }

    // function to complete the data in the document
    public function checking() {
        $check_in_value = $this->registry['request']->get('check_in_value');

        // define and load custom plugin lang files
        $files[] = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini';
        $this->loadI18NFiles($files);

        // check for a document for this day
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters = array('where'        => array('d.type="' . $this->type_id . '"',
                                                 'DATE_FORMAT(d.added, "%Y-%m-%d")="' . date("Y-m-d") . '"'
                                                ),
                         'model_lang'   => $this->registry['lang']);
        $current_document = Documents::searchOne($this->registry, $filters);

        if (!$current_document) {
            $this->createDocument();
        }

        $employee_ids = array($this->registry['request']->get('employee_id'));

        $this->getRequiredEmployees($employee_ids);
        $this->checkCurrentStatus();

        $function_name = 'check' . ucfirst($check_in_value);

        $result = $this->$function_name();

        if (!$result) {
            $this->raiseError('error_plugin_multiple_check_in_wrong_data_record');
        }

        $checking_result = array(
            'errors' => array()
        );

        $errors = $this->registry['messages']->getErrors();
        foreach ($errors as $key => $error) {
            $checking_result['errors'][$key] = $error;
        }

        return json_encode($checking_result);
    }

    /*
     * Create a document for checking when such doesn't exist
     */
    public function createDocument() {
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

        $request = $this->registry['request'];

        $document = Documents::buildModel($this->registry);

        $filters_type = array('where' => array('dt.id = ' . $this->type_id,
                                               'dt.active = 1',
                                               'dt.inheritance = 0'),
                              'sanitize'   => true,
                              'model_lang' => $request->get('model_lang'));
        $document_type = Documents_Types::searchOne($this->registry, $filters_type);

        if ($document_type->get('default_name')) {
            $doc_name = $document_type->get('default_name');
        } else {
            $doc_name = $this->registry['translater']->translate('plugin_multiple_check_in_default_name');
        }

        $doc_customer = '';
        if ($document_type->get('default_customer')) {
            $doc_customer = $document_type->get('default_customer');
        }

        if ($doc_name && $doc_customer) {
            $document->set('name', $doc_name, true);
            $document->set('type', $document_type->get('id'), true);
            $document->set('customer', $doc_customer, true);
            $document->set('active', 1, true);
            $document->set('department', $document_type->getDefaultDepartment(), true);
            $document->set('group', $document_type->getDefaultGroup(), true);
            $document->set('media', $document_type->get('default_media'), true);
            $document->set('employee', $this->registry['currentUser']->get('id'), true);
            $document->set('office', 0, true);
            $document->set('custom_num', '', true);

            $result = $document->save();
            if ($result) {
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                $filters = array('where' => array('d.id = ' . $document->get('id')),
                                 'model_lang' => $document->get('model_lang'));
                $new_document = Documents::searchOne($this->registry, $filters);
                $old_model = new Document($this->registry);
                $old_model->sanitize();
                $audit_parent = Documents_History::saveData($this->registry, array('model' => $document, 'action_type' => 'add', 'new_model' => $new_document, 'old_model' => $old_model));
            } else {
                $this->raiseError('error_plugin_multiple_check_in_document_create');
            }
        } else {
            $this->raiseError('error_plugin_multiple_check_in_document_no_name');
            $result = false;
        }

        return $result;
    }

    /*
     * Function to check in the current user
     */
    public function checkIn() {
        //finds the documents
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters = array('where'        => array('d.type="' . $this->type_id . '"',
                                                 'DATE_FORMAT(d.added, "%Y-%m-%d")="' . date("Y-m-d") . '"'
                                                ),
                         'model_lang'   => $this->registry['lang']);
        $current_document = Documents::searchOne($this->registry, $filters);

        $old_document = clone $current_document;

        $current_user_employee = $this->registry['request']->get('employee_id');
        $curent_employees_info = $this->get('employees_data');
        $employee_info = $curent_employees_info[$current_user_employee];
        $user_var = $employee_info['user_var'];
        $start_var = $employee_info['start_var'];

        //take the additional variables
        $current_document->getVars();
        $vars = $current_document->get('vars');

        $user_key = '';
        $arrive_key = '';
        foreach ($vars as $key => $var) {
            if (!$user_key || !$arrive_key) {
                if ($var['id'] == $user_var) {
                    $user_key = $key;
                }
                if ($var['id'] == $start_var) {
                    $arrive_key = $key;
                }
            } else {
                break;
            }
        }

        // find the first empty row or add a new row if an empty cell doesn't exist
        $user_var_properties = $vars[$user_key];
        $start_var_properties = $vars[$arrive_key];

        if (in_array('', $user_var_properties['value'])) {
            $position_row = array_search('', $user_var_properties['value']);
        } else {
            if (count($user_var_properties['value'])) {
                $position_row = count($user_var_properties['value']) + 1;
            } else {
                $position_row = 1;
            }
        }

        $user_var_properties['value'][$position_row] = $current_user_employee;
        $start_var_properties['value'][$position_row] = date("H:i", time());

        $vars[$user_key] = $user_var_properties;
        $vars[$arrive_key] = $start_var_properties;

        $current_document->set('vars', $vars, true);

        $result = $current_document->saveVars();

        if ($result) {
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            $filters = array('where' => array('d.id = ' . $current_document->get('id')),
                             'model_lang' => $current_document->get('model_lang'));
            $new_document = Documents::searchOne($this->registry, $filters);
            $new_document->getVars();

            $audit_parent = Documents_History::saveData($this->registry, array('model' => $current_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));
        } else {
            $this->raiseError('error_plugin_multiple_check_in_document_create');
        }

        return $result;
    }

    /*
     * Function to check in the current user
     */
    public function checkOut() {
        //finds the documents
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters = array('where'        => array('d.type="' . $this->type_id . '"',
                                                 'DATE_FORMAT(d.added, "%Y-%m-%d")="' . date("Y-m-d") . '"'
                                                ),
                         'model_lang'   => $this->registry['lang']);
        $current_document = Documents::searchOne($this->registry, $filters);

        $old_document = clone $current_document;

        if ($current_document) {
            $current_user_employee = $this->registry['request']->get('employee_id');
            $curent_employees_info = $this->get('employees_data');
            $employee_info = $curent_employees_info[$current_user_employee];
            $user_var = $employee_info['user_var'];
            $finish_var = $employee_info['finish_var'];

            //take the additional variables
            $current_document->getVars();
            $vars = $current_document->get('vars');

            $user_key = '';
            $left_key = '';
            foreach ($vars as $key => $var) {
                if (!$user_key || !$left_key) {
                    if ($var['id'] == $user_var) {
                        $user_key = $key;
                    }
                    if ($var['id'] == $finish_var) {
                        $left_key = $key;
                    }
                } else {
                    break;
                }
            }

            // find the first empty row or add a new row if an empty cell doesn't exist
            $user_var_properties = $vars[$user_key];
            $finish_var_properties = $vars[$left_key];

            $position_row = array_search($current_user_employee, $user_var_properties['value']);

            $finish_var_properties['value'][$position_row] = date("H:i", time());

            $vars[$left_key] = $finish_var_properties;

            $current_document->set('vars', $vars, true);

            $result = $current_document->saveVars();

            if ($result) {
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

                $filters = array('where' => array('d.id = ' . $current_document->get('id')),
                                 'model_lang' => $current_document->get('model_lang'));
                $new_document = Documents::searchOne($this->registry, $filters);
                $new_document->getVars();

                $audit_parent = Documents_History::saveData($this->registry, array('model' => $current_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));
            } else {
                $this->raiseError('error_plugin_multiple_check_in_document_create');
            }
        } else {
            $this->raiseError('error_plugin_multiple_check_in_no_document');

            $result = false;
        }

        return $result;
    }
}
?>