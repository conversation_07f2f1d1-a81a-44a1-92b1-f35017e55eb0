<?php

class Target_Categories extends Reports {

    private static $i18n;

    public static function buildQuery(&$registry, $filters = array()) {

        // set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            // default model language is the interface language
            $model_lang = $registry['lang'];
        }
        $db = &$registry['db'];

        self::$i18n = &$registry['translater'];

        $errors = false;

        // flag whether to search by protocol customer
        $search_protocol_customer = defined('CUSTOMER_ID_VAR') && CUSTOMER_ID_VAR;

        // prepare ids of additional variables
        $add_vars = array(
            CATEGORY_VAR,
            CATEGORY_NAME_VAR,
            CFACE_VAR,
            VFACE_VAR,
            PICTURE_VAR,
            NOTES_VAR
        );
        if ($search_protocol_customer) {
            $add_vars[] = CUSTOMER_ID_VAR;
            if (defined('CUSTOMER_NAME_VAR')) {
                $add_vars[] = CUSTOMER_NAME_VAR;
            }
        }
        $add_vars = array_combine($add_vars, array_fill(0, count($add_vars), 0));
        $query =
            'SELECT fm.name, fm.id, TRIM(fi.content) AS label' . "\n" .
            'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi' . "\n" .
            '  ON fm.id = fi.parent_id AND fi.lang = \'' . $model_lang . '\' AND fi.content_type = \'label\'' . "\n" .
            'WHERE fm.model=\'Document\' AND fm.model_type=\'' . DOCUMENT_TYPE_VISIT . '\'' . "\n" .
            '  AND fm.name IN (\'' . implode('\', \'', array_keys($add_vars)) . '\')';
        $add_vars = array_merge($add_vars, $db->GetAssoc($query));

        // prepare labels and ids
        $labels = array_map(function($a) { return !empty($a) ? $a['label'] : ''; }, $add_vars);
        $add_vars = array_map(function($a) { return !empty($a) ? $a['id'] : $a; }, $add_vars);

        // validate filter data
        if (empty($filters['date_from']) && empty($filters['date_to']) ||
        $filters['date_to'] && $filters['date_from'] > $filters['date_to'] ||
        $search_protocol_customer && empty($filters['protocol_customer'])) {
            $registry['messages']->setError(self::$i18n->translate('error_reports_complete_required_filters'));
            $errors = true;
        }

        $final_results = array();

        if (!$errors) {
            $query =
                'SELECT d.id, d.date, d.customer, d.trademark,' . "\n" .
                'dcstm_cat.value AS category, dcstm_catn.value AS category_name,' . "\n" .
                'CONVERT(REPLACE(dcstm_vface.value, \',\', \'.\'), DECIMAL(10, 2)) AS vface,' . "\n" .
                'CONVERT(REPLACE(dcstm_cface.value, \',\', \'.\'), DECIMAL(10, 2)) AS cface,' . "\n" .
                'dcstm_pic.value AS picture, files.path, files.filename, 0 AS is_image,' . "\n" .
                'dcstm_notes.value AS notes,' . "\n" .
                'TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) AS customer_name,' . "\n" .
                'ni18n.name AS trademark_name, dti18n.name AS type_name,' . "\n" .
                'TRIM(CONCAT(ui18n1.firstname, " ", ui18n1.lastname)) AS added_by_name' . "\n" .
                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_cat' . "\n" .
                '  ON d.id=dcstm_cat.model_id AND dcstm_cat.var_id=' . $add_vars[CATEGORY_VAR] . "\n" .
                'JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tags' . "\n" .
                '  ON dcstm_cat.value=tags.model_id AND tags.model=\'Nomenclature\'' . "\n" .
                '    AND tags.tag_id=\'' . CATEGORY_TAG . '\'' . "\n" .
                ($search_protocol_customer ?
                'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_pcust' . "\n" .
                '  ON d.id = dcstm_pcust.model_id AND dcstm_pcust.var_id = \'' . $add_vars[CUSTOMER_ID_VAR] . '\'' . "\n" .
                '    AND dcstm_pcust.value = \'' . $filters['protocol_customer'] . '\'' . "\n" :
                '') .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_catn' . "\n" .
                '  ON dcstm_cat.model_id=dcstm_catn.model_id' . "\n" .
                '    AND dcstm_cat.num=dcstm_catn.num' . "\n" .
                '    AND dcstm_catn.var_id=' . $add_vars[CATEGORY_NAME_VAR] . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_cface' . "\n" .
                '  ON dcstm_catn.model_id=dcstm_cface.model_id' . "\n" .
                '    AND dcstm_catn.num=dcstm_cface.num' . "\n" .
                '    AND dcstm_cface.var_id=\'' . $add_vars[CFACE_VAR] . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_vface' . "\n" .
                '  ON dcstm_cface.model_id=dcstm_vface.model_id' . "\n" .
                '    AND dcstm_cface.num=dcstm_vface.num' . "\n" .
                '    AND dcstm_vface.var_id=\'' . $add_vars[VFACE_VAR] . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_pic' . "\n" .
                '  ON dcstm_vface.model_id=dcstm_pic.model_id' . "\n" .
                '    AND dcstm_vface.num=dcstm_pic.num' . "\n" .
                '    AND dcstm_pic.var_id=\'' . $add_vars[PICTURE_VAR] . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FILES . ' AS files' . "\n" .
                '  ON files.id=dcstm_pic.value AND files.deleted_by=0' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_notes' . "\n" .
                '  ON dcstm_pic.model_id=dcstm_notes.model_id' . "\n" .
                '    AND dcstm_pic.num=dcstm_notes.num' . "\n" .
                '    AND dcstm_notes.var_id=\'' . $add_vars[NOTES_VAR] . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                '  ON d.type=dti18n.parent_id AND dti18n.lang=\'' . $model_lang . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                '  ON d.customer=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                '  ON d.trademark=ni18n.parent_id AND ni18n.lang=\'' . $model_lang . '\'' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                '  ON d.added_by=ui18n1.parent_id AND ui18n1.lang=\'' . $model_lang . '\'' . "\n";

            // prepare search conditions
            $where = array();
            $where[] = 'd.type = \'' . DOCUMENT_TYPE_VISIT . '\'';
            $where[] = 'd.active = 1';
            $where[] = 'd.deleted_by = 0';
            if (count(array_filter(array($filters['date_from'], $filters['date_to']))) == 1) {
                // if only one of the fields is set, search for exact date
                $where[] = 'd.date = "' . ($filters['date_from'] ?: $filters['date_to']) . '"';
            } else {
                // if both fields are set, search within period
                $where[] = 'd.date >= "' . $filters['date_from'] . '" AND d.date <= "' . $filters['date_to'] . '"';
            }
            if (!empty($filters['customer']) && array_filter($filters['customer'])) {
                $where[] = 'd.customer IN (\'' . implode('\', \'', array_unique(array_filter($filters['customer']))) . '\')';
            }
            if (!empty($filters['trademark']) && array_filter($filters['trademark'])) {
                $where[] = 'd.trademark IN (\'' . implode('\', \'', array_unique(array_filter($filters['trademark']))) . '\')';
            }

            $query .= 'WHERE ' . implode("\n" . '  AND ', $where) . "\n" .
                      'ORDER BY d.date ASC, d.id ASC';

            $results = $db->GetAll($query);

            // collect all displayed categories
            $categories = array();

            foreach ($results as $result) {
                if (!isset($final_results[$result['customer']])) {
                    $final_results[$result['customer']] = array(
                        'name' => $result['customer_name'],
                        'rowspan' => 0,
                        'objects' => array()
                    );
                }
                if (!isset($final_results[$result['customer']]['objects'][$result['trademark']])) {
                    $final_results[$result['customer']]['objects'][$result['trademark']] = array(
                        'name' => $result['trademark_name'],
                        'rowspan' => 0,
                        'visits' => array(),
                        // aggregated data for second table
                        'categories_avg' => array(),
                        'vface_total' => 0,
                        'cface_total' => 0
                    );
                }
                if (!isset($final_results[$result['customer']]['objects'][$result['trademark']]['visits'][$result['id']])) {
                    $final_results[$result['customer']]['objects'][$result['trademark']]['visits'][$result['id']] = array(
                        'name' => $result['type_name'],
                        'date' => $result['date'],
                        'added_by_name' => $result['added_by_name'],
                        'rowspan' => 0,
                        'categories' => array()
                    );
                }
                if ($result['picture'] && $result['path']) {
                    if (!file_exists($result['path'])) {
                        $result['picture'] = $result['path'] = $result['filename'] = '';
                    } else {
                        $f = new File($registry,
                            array('id' => $result['picture'], 'path' => $result['path'], 'filename' => $result['filename']));
                        $result['is_image'] = $f->isImage();
                        $result['icon_name'] = $f->getIconName();
                        unset($f);
                    }
                } elseif ($result['picture']) {
                    $result['picture'] = '';
                }
                $result['vface'] = round($result['vface'], 2);
                $result['cface'] = round($result['cface'], 2);
                $result['percentage'] =
                    $result['vface'] + $result['cface'] > 0 ?
                    round($result['vface'] * 100 / ($result['vface'] + $result['cface']), 2) :
                    0;
                $result['name'] = $result['category_name'];
                $final_results[$result['customer']]['objects'][$result['trademark']]['visits'][$result['id']]['categories'][] =
                    array_intersect_key($result, array_flip(array(
                        'category',
                        'name',
                        'vface',
                        'cface',
                        'percentage',
                        'picture',
                        'path',
                        'filename',
                        'is_image',
                        'icon_name',
                        'notes'
                    )));

                $final_results[$result['customer']]['rowspan']++;
                $final_results[$result['customer']]['objects'][$result['trademark']]['rowspan']++;
                $final_results[$result['customer']]['objects'][$result['trademark']]['visits'][$result['id']]['rowspan']++;

                // collect data for aggregates table
                if (!isset($final_results[$result['customer']]['objects'][$result['trademark']]['categories_avg'][$result['category']])) {
                    $final_results[$result['customer']]['objects'][$result['trademark']]['categories_avg'][$result['category']] =
                        array(
                            'vface' => array(),
                            'cface' => array()
                        );
                }
                $final_results[$result['customer']]['objects'][$result['trademark']]['categories_avg'][$result['category']]['vface'][] =
                    $result['vface'];
                $final_results[$result['customer']]['objects'][$result['trademark']]['categories_avg'][$result['category']]['cface'][] =
                    $result['cface'];

                // collect unique categories
                if (!array_key_exists($result['category'], $categories)) {
                    $categories[$result['category']] = $result['category_name'];
                }
            }

            // sort by customer and trademark names ascending
            $sortByName = function ($a, $b) {
                return $a['name'] > $b['name'];
            };
            foreach ($final_results as $cid => $result) {
                uasort($final_results[$cid]['objects'], $sortByName);
                array_walk($final_results[$cid]['objects'], function(&$object, $oid) {
                    array_walk($object['categories_avg'], function(&$v, $k) use (&$object) {
                        $v['vface'] = !empty($v['vface']) ? round(array_sum($v['vface'])/count($v['vface']), 2) : 0;
                        $v['cface'] = !empty($v['cface']) ? round(array_sum($v['cface'])/count($v['cface']), 2) : 0;
                        $v['percentage'] =
                            $v['vface'] + $v['cface'] > 0 ?
                            round($v['vface'] * 100 / ($v['vface'] + $v['cface']), 2) :
                            0;
                        $object['vface_total'] += $v['vface'];
                        $object['cface_total'] += $v['cface'];
                    });
                    $object['percentage_total'] =
                        $object['vface_total'] + $object['cface_total'] > 0 ?
                        round($object['vface_total'] * 100 / ($object['vface_total'] + $object['cface_total']), 2) :
                        0;
                });
            }
            uasort($final_results, $sortByName);

            if ($final_results) {
                // add collected categories to final results
                asort($categories, SORT_STRING);
                $final_results['additional_options']['categories'] = $categories;
            }
        }
        $final_results['additional_options']['labels'] = $labels;

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }

        return $results;
    }
}
