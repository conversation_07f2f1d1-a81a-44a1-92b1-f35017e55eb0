<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    protected function _index() {

        $user = $this->registry['request']->get('user_responsible');
        $customers = $this->registry['request']->get('items');
        if ($this->registry['request']->isPost() && $user && !empty($customers)) {

            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
            require_once PH_MODULES_DIR . 'tasks/models/tasks.history.php';
            require_once PH_MODULES_DIR . 'tasks/models/tasks.audit.php';
            require_once PH_MODULES_DIR . 'automations/controllers/automations.controller.php';
            require_once PH_MODULES_DIR . 'automations/plugins/ecom/controllers/ecom.automations.controller.php';

            //load plugin i18n files
            $report = $this->getReportType();
            $report = Reports::getReports($this->registry, array('name' => $report['name']));
            $report = $report[0];
            $i18n_file = sprintf('%s%s%s%s%s%s',
                    PH_MODULES_DIR,
                    'reports/plugins/',
                    $report->get('type'),
                    '/i18n/',
                    $this->registry['lang'],
                    '/reports.ini');
            $this->registry['translater']->loadFile($i18n_file);
            $this->registry['db']->StartTrans();
            $props = array(
                'name' => $this->i18n('reports_waves'),
                'planned_start_date' => date('Y-m-d H:i:00'),
                'planned_finish_date' => date_add(date_create(), new DateInterval('P45D'))->format('Y-m-d H:i:00'),
                'description' => $this->i18n('reports_waves_description'),
                'status' => 'planning',
                'group' => 1,
                'department' => 1,
            );
            $others = array(2 => array('new' => 5, 'old' => '10'), 5 => array('new' => 15, 'old' => 19));

            // Prepare the automations controller to use some automations
            $automation = new Ecom_Automations_Controller($this->registry);

            // Prepare to use the automation: assignUserToCustomer
            if (method_exists($automation, 'assignUserToCustomer')) {
                $query = 'SELECT a.start_model_type, a.* FROM ' . DB_TABLE_AUTOMATIONS . ' AS a WHERE module="tasks" AND method LIKE "%assignUserToCustomer%" AND a.active=1';
                $records = $this->registry['db']->GetAssoc($query);
            }

            // Prepare to collect a list of users, which have been notified for assignments
            $assigned_users_notified = array();

            foreach ($customers as $c) {
                $props['customer']= $c;
                foreach ($others as $type => $substatus) {
                    //fail current active tasks for the type and customer
                    $query = 'UPDATE ' . DB_TABLE_TASKS . ' SET status = "finished", substatus = ' . $substatus['old'] . "\n" .
                             'WHERE customer = ' . $c . ' AND status != "finished" AND type = ' . $type;
                    $this->registry['db']->Execute($query);
                    if ($this->registry['db']->ErrorMsg()) {
                        $this->registry['db']->FailTrans();
                    }
                    $props['type'] = $type;
                    $task = new Task($this->registry, $props);
                    if ($task->save()) {
                        //update substatus and assignments
                        $query = 'UPDATE ' . DB_TABLE_TASKS . ' SET substatus = ' . $substatus['new'] . ' WHERE id = ' . $task->get('id');
                        $this->registry['db']->Execute($query);
                        $task = Tasks::searchOne($this->registry, array('where' => array('t.id = ' . $task->get('id'))));
                        $task->set('assignments_owner', array($user), true);

                        // If the current user has already been notified
                        if (in_array($user, $assigned_users_notified)) {
                            // Do not display message for the assignment
                            $display_messages = false;
                        } else {
                            // Display message for the assignment
                            $display_messages = true;
                        }

                        // Assign the user
                        if ($task->assign(true, $display_messages)) {
                            // Collect notified users
                            $assigned_users_notified[] = $user;

                            $this->registry->set('getAssignments', true, true);
                            $old_model = new Task($this->registry, array());
                            $task = Tasks::searchOne($this->registry, array('where' => array('t.id = ' . $task->get('id'))));
                            $this->registry->set('getAssignments', false, true);
                            //write some hstory
                            Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'add', 'new_model' => $task, 'old_model' => $old_model));

                            //call automations needed for the actions above
                            if (isset($records[$type])) {
                                $automation->getSettings($records[$type]);
                                $params = array(
                                    'custom_call' => true,
                                    'model' => $task,
                                );
                                $automation->assignUserToCustomer($params);
                            }
                        } else {
                            $this->registry['db']->FailTrans();
                        }
                    } else {
                        $this->registry['db']->FailTrans();
                    }
                }
            }

            // Call the automation: clientsActiveInactive
            if (method_exists($automation, 'clientsActiveInactive')) {
                $query = 'SELECT * FROM ' . DB_TABLE_AUTOMATIONS . ' WHERE method LIKE "%clientsActiveInactive%" AND `active`=1';
                $record = $this->registry['db']->GetRow($query);
                if (!empty($record)) {
                    $automation->getSettings($record);
                    $params = array(
                        'custom_call' => true,
                        'customers' => $customers,
                    );
                    $automation->clientsActiveInactive($params);
                }
            }

            $result = !$this->registry['db']->HasFailedTrans();
            $this->registry['db']->CompleteTrans();
            if ($result) {
                $this->registry['messages']->setMessage($this->i18n('reports_tasks_added'));
            } else {
                $this->registry['messages']->setMessage($this->i18n('reports_tasks_failed'), -1);
            }
        }
        parent::_index();
    }
}

?>
