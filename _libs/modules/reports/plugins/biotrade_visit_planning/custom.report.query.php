<?php
    class Biotrade_Visit_Planning extends Reports {

        private static $i18n;

        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }
            $db = &$registry['db'];

            self::$i18n = &$registry['translater'];

            //IMPORTANT: use these flags to check if the results should be calculated (instead of $registry['messages']->getErrors())
            $errors = false;

            // filters are passed from saved dashlet filters, prepare some values and set them in session as well
            if (isset($filters['agent']) && $filters['agent'] == '<currentUser>') {
                // if agent is specified as placeholder, replace it with id of current user
                $filters['agent'] = $registry['currentUser']->get('id');
                // set today as both start and end filter date and search in currently active period
                $filters['date_from'] = $filters['date_to'] = date('Y-m-d');
                $filters['date_from_formatted'] = $filters['date_to_formatted'] = General::strftime();

                $session_param = 'reports_' . $filters['report_type'] . '_report';
                $filters['generated'] = $registry['session']->get('generated', $session_param);
                $registry['session']->set($session_param, $filters, '', true);

                // a flag that the dates will be replaced with start and end of current period (when we get them)
                $filters['fix_period'] = true;
            }

            // validate filter data
            if (empty($filters['search_by']) || !in_array($filters['search_by'], array(1, 2))
                || $filters['search_by'] == 1 && empty($filters['date'])
                || $filters['search_by'] == 2 && (empty($filters['date_from']) || empty($filters['date_to']) || $filters['date_from'] > $filters['date_to'])) {
                $registry['messages']->setError(self::$i18n->translate('error_reports_complete_required_filters'));
                $errors = true;
            } elseif (!empty($filters['create_daily_schedule']) && (empty($filters['agent']) || empty($filters['ray']) || $filters['search_by'] != 1)) {
                $registry['messages']->setError(self::$i18n->translate('error_reports_create_daily_schedule'));
                $errors = true;
            }

            if (!$errors) {
                //get all defined constants in the application
                $constants = get_defined_constants(true);
                //filter only user defined constants
                $constants = $constants['user'];

                // define entities
                $entities = array(
                    'pharmacy' => array('model' => 'Customer', 'model_type' => CUST_TYPE_PHARMACY),
                    'doctor' => array('model' => 'Customer', 'model_type' => CUST_TYPE_DOCTOR),
                    'target_visit' => array('model' => 'Document', 'model_type' => DOC_TYPE_TARGET_VISIT),
                    'target_campaign' => array('model' => 'Document', 'model_type' => DOC_TYPE_TARGET_CAMPAIGN),
                    'ray' => array('model' => 'Nomenclature', 'model_type' => NOM_TYPE_RAY),
                    'region' => array('model' => 'Nomenclature', 'model_type' => NOM_TYPE_REGION)
                );
                if (!empty($filters['create_daily_schedule'])) {
                    $entities['daily_schedule'] = array('model' => 'Document', 'model_type' => DOC_TYPE_DAILY_SCHEDULE);
                }

                // get ids of additional vars from constants
                $add_vars = array();
                $num_vars = 0;
                $matches = array();
                foreach ($constants as $key => $value) {
                    if (preg_match('#^(' . implode('|', array_keys($entities)) . ')_.*_var#i', $key, $matches)) {
                        $matches[1] = strtolower($matches[1]);
                        if (empty($add_vars[$matches[1]])) {
                            $add_vars[$matches[1]] = array();
                        }
                        $add_vars[$matches[1]][constant($key)] = $value;
                        $num_vars++;
                    }
                }

                // missing settings for additional variables
                if (count($add_vars) != count($entities)) {
                    $registry['messages']->setError(self::$i18n->translate('error_report_not_set'));
                    $errors = true;
                } else {
                    $where = array();
                    foreach ($entities as $entity => $props) {
                        $condition = '(';
                        foreach ($props as $k => $v) {
                            $condition .= $k . '="' . $v . '" AND ';
                        }
                        $condition .= 'name IN ("' . implode('", "', $add_vars[$entity]) . '"))';
                        $where[] = $condition;
                    }
                    $where = implode(' OR ' . "\n", $where);

                    $query = 'SELECT CONCAT(model, model_type) AS model, name, id' . "\n" .
                             'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                             'WHERE ' . $where;
                    $add_vars_data = $db->GetAll($query);

                    // settings do not match variable names
                    if (count($add_vars_data) != $num_vars) {
                        $registry['messages']->setError(self::$i18n->translate('error_report_not_set'));
                        $errors = true;
                    } else {
                        foreach ($entities as $entity => $props) {
                            $mkey = $props['model'] . $props['model_type'];
                            $entities[$entity]['vars'] = array();
                            foreach ($add_vars_data as $idx => $data) {
                                if ($data['model'] == $mkey) {
                                    $setting_name = array_search($data['name'], $add_vars[$entity]);
                                    if ($setting_name !== false) {
                                        $entities[$entity]['vars'][$setting_name] = $data['id'];
                                    }
                                    unset($add_vars_data[$idx]);
                                }
                            }
                        }

                        // aliases of vars arrays
                        $pharmacy_vars = &$entities['pharmacy']['vars'];
                        $doctor_vars = &$entities['doctor']['vars'];
                        $target_visit_vars = &$entities['target_visit']['vars'];
                        $target_campaign_vars = &$entities['target_campaign']['vars'];

                        // get latest target visit document
                        $search_date = $filters['search_by'] == 1 ? $filters['date'] : $filters['date_to'];
                        $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                 '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                 '  AND dcstm_fd.value <= \'' . $search_date . '\'' . "\n" .
                                 'ORDER BY dcstm_fd.value DESC, d.id DESC' . "\n" .
                                 'LIMIT 1';
                        $target_visit = $db->GetRow($query);
                        // validate lower boundary of date/date_to filter
                        if (!$target_visit) {
                            $registry['messages']->setError(self::$i18n->translate('error_reports_no_target_visit'));
                            $errors = true;
                        } else {
                            // searched date should not be after end of next period after the one that includes current date
                            $today = date('Y-m-d');
                            $period_search_date = $search_date > $today ? $today : $search_date;
                            // calculate start and end of period that searched date is in
                            $target_visit['period_from'] = $target_visit['from_date'];
                            // get first date of month for 'from_date' and calculate periods as if document always starts from there
                            $target_visit['next_period_from'] = date_add(
                                date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $target_visit['from_date'])),
                                new DateInterval('P' . $target_visit['months_num'] . 'M')
                            )->format('Y-m-d');
                            while ($target_visit['next_period_from'] <= $period_search_date) {
                                $target_visit['period_from'] = $target_visit['next_period_from'];
                                $target_visit['next_period_from'] = date_add(date_create($target_visit['period_from']), new DateInterval('P' . $target_visit['months_num'] . 'M'))->format('Y-m-d');
                            }
                            $target_visit['period_to'] = date_sub(date_create($target_visit['next_period_from']), new DateInterval('P1D'))->format('Y-m-d');

                            // calculate end of next period as start of next period + months_num months - 1 day
                            $next_period_to = date_add(date_create($target_visit['next_period_from']), new DateInterval('P' . $target_visit['months_num'] . 'M'))->format('Y-m-d');
                            $next_period_to = date_sub(date_create($next_period_to), new DateInterval('P1D'))->format('Y-m-d');
                            // if searched date is after end of last found period and until end of next period, shift dates forward with one more period
                            if ($search_date > $target_visit['period_to'] && $search_date <= $next_period_to) {
                                $target_visit['period_from'] = $target_visit['next_period_from'];
                                $target_visit['period_to'] = $next_period_to;
                            }
                            unset($target_visit['next_period_from']);

                            // validate upper boundary of date/date_to filter
                            if ($search_date > $next_period_to) {
                                $registry['messages']->setError(sprintf(self::$i18n->translate('error_reports_target_visit_date'), General::strftime('%d.%m.%Y', $next_period_to)));
                                $errors = true;
                            }
                        }
                    }
                }
            }

            $final_results = array();

            if (!$errors) {

                // get rays to search in
                $rays = array();
                if (!empty($filters['ray'])) {
                    $rays[] = $filters['ray'];
                } elseif (!empty($filters['agent'])) {
                    $rays = self::getRays($registry, $filters['agent'], false);
                    // sales rep without rays?
                    if (!$rays) {
                        $rays[] = 0;
                    }
                }

                // get class tag names
                $query = 'SELECT t.id, ti18n.name' . "\n" .
                         'FROM ' . DB_TABLE_TAGS . ' AS t' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n' . "\n" .
                         '  ON t.id=ti18n.parent_id AND ti18n.lang=\'' . $model_lang . '\'' . "\n" .
                         'WHERE t.id IN (' . CLASS_TAGS . ')';
                $class_tags = $db->GetAssoc($query);

                // get ids of sales representative users (including inactive and deleted)
                $query = 'SELECT id FROM ' . DB_TABLE_USERS . ' WHERE role=\'' . ROLE_SALES . '\'';
                $sales_reps = $db->GetCol($query);

                if ($filters['search_by'] == 1) {
                    // search by date

                    // 1 - Monday ... 7 - Sunday
                    $date_of_week = date('w', strtotime($filters['date']));
                    if ($date_of_week == 0) {
                        $date_of_week = 7;
                    }
                    // 9 - odd or 8 - even
                    $date_odd_even = (intval(substr($filters['date'], 8, 2)) % 2) ? WORK_TIME_ODD : WORK_TIME_EVEN;
                    // prepare condition for where clause
                    $doctor_workdays = $date_of_week . ', ' . $date_odd_even;

                    // get pharmacies
                    $query = 'SELECT CONCAT(c.id, \'_0\') AS idx, c.id, c.type, c.phone, c.gsm, ci18n.name,' . "\n" .
                             '  ccstm_ray.value AS ray_id, ccstm_address.value AS address,' . "\n" .
                             '  CASE \'' . $date_of_week . '\' WHEN 6 THEN ccstm_sat.value WHEN 7 THEN ccstm_sun.value ELSE ccstm_week.value END AS worktime,' . "\n" .
                             '  CASE \'' . $date_of_week . '\' WHEN 6 THEN ccstm_sat_to.value WHEN 7 THEN ccstm_sun_to.value ELSE ccstm_week_to.value END AS worktime_to' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                             '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id=\'' . $pharmacy_vars[PHARMACY_RAY_ID_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_address' . "\n" .
                             '  ON c.id=ccstm_address.model_id AND ccstm_address.var_id=\'' . $pharmacy_vars[PHARMACY_ADDRESS_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_workdays' . "\n" .
                             '  ON c.id=ccstm_workdays.model_id AND ccstm_workdays.var_id=\'' . $pharmacy_vars[PHARMACY_WORKDAYS_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_week' . "\n" .
                             '  ON c.id=ccstm_week.model_id AND ccstm_week.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_WEEK_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_week_to' . "\n" .
                             '  ON c.id=ccstm_week_to.model_id AND ccstm_week_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_WEEK__TO_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sat' . "\n" .
                             '  ON c.id=ccstm_sat.model_id AND ccstm_sat.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SAT_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sat_to' . "\n" .
                             '  ON c.id=ccstm_sat_to.model_id AND ccstm_sat_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SAT__TO_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sun' . "\n" .
                             '  ON c.id=ccstm_sun.model_id AND ccstm_sun.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SUN_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_sun_to' . "\n" .
                             '  ON c.id=ccstm_sun_to.model_id AND ccstm_sun_to.var_id=\'' . $pharmacy_vars[PHARMACY_WORKTIME_SUN__TO_VAR] . '\'' . "\n" .
                             'WHERE c.type=\'' . CUST_TYPE_PHARMACY . '\' AND c.active=1 AND c.deleted_by=0' . "\n" .
                             '  AND (ccstm_workdays.value IS NULL OR ccstm_workdays.value="" OR ccstm_workdays.value LIKE \'%' . $date_of_week . '%\')' . "\n" .
                             ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                             'GROUP BY c.id';
                    $targets = $db->GetAssoc($query);

                    // prepare var ids for iterative searches for each workplace
                    $workplaces = array();
                    // prepare condition for where clause for doctors with no data for working hours
                    $doctor_no_data = array();
                    foreach (array(1, 2, 3) as $place) {
                        $workplaces[$place] = array();
                        foreach (array(1, 2, 3) as $time) {
                            $workplaces[$place]['t' . $time] = $doctor_no_data[] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_VAR')];
                            $workplaces[$place]['t' . $time . '_from'] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_FROM_VAR')];
                            $workplaces[$place]['t' . $time . '_to'] = $doctor_vars[constant('DOCTOR_P' . $place . '_T' . $time . '_TO_VAR')];
                        }
                    }
                    $doctor_no_data = '\'' . implode('\', \'', $doctor_no_data) . '\'';

                    // get doctors
                    foreach ($workplaces as $place => $times) {
                        $query = 'SELECT CONCAT(c.id, \'_\', ccstm_ray.num) AS idx, c.id, c.type, c.phone, c.gsm,' . "\n" .
                                 '  ci18n.name, ccstm_ray.value AS ray_id,' . "\n" .
                                 '  CONCAT(IF(ccstm_place.value IS NOT NULL, ccstm_place.value, \'\'), \', \', ' . "\n" .
                                 '    IF(ccstm_address.value IS NOT NULL, ccstm_address.value, \'\'), \' (\', ' . "\n" .
                                 '    IF(ccstm_city.value IS NOT NULL, ccstm_city.value, \'\'), \')\') AS address, ' . "\n" .
                                 '  IF(ccstm_t1.value IN (' . $doctor_workdays . '), ccstm_t1_from.value, IF(ccstm_t2.value IN (' . $doctor_workdays . '), ccstm_t2_from.value, ccstm_t3_from.value)) AS worktime,' . "\n" .
                                 '  IF(ccstm_t1.value IN (' . $doctor_workdays . '), ccstm_t1_to.value, IF(ccstm_t2.value IN (' . $doctor_workdays . '), ccstm_t2_to.value, ccstm_t3_to.value)) AS worktime_to' . "\n" .
                                 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                                 '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id=\'' . $doctor_vars[DOCTOR_RAY_ID_VAR] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_place' . "\n" .
                                 '  ON c.id=ccstm_place.model_id AND ccstm_place.var_id=\'' . $doctor_vars[DOCTOR_PLACE_VAR] . '\' AND ccstm_ray.num=ccstm_place.num' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_city' . "\n" .
                                 '  ON c.id=ccstm_city.model_id AND ccstm_city.var_id=\'' . $doctor_vars[DOCTOR_CITY_VAR] . '\' AND ccstm_ray.num=ccstm_city.num' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_address' . "\n" .
                                 '  ON c.id=ccstm_address.model_id AND ccstm_address.var_id=\'' . $doctor_vars[DOCTOR_ADDRESS_VAR] . '\' AND ccstm_ray.num=ccstm_address.num' . "\n" .
                                 // config fields
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1' . "\n" .
                                 '  ON c.id=ccstm_t1.model_id AND ccstm_t1.var_id=\'' . $times['t1'] . '\' AND ccstm_t1.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1_from' . "\n" .
                                 '  ON c.id=ccstm_t1_from.model_id AND ccstm_t1_from.var_id=\'' . $times['t1_from'] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t1_to' . "\n" .
                                 '  ON c.id=ccstm_t1_to.model_id AND ccstm_t1_to.var_id=\'' . $times['t1_to'] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2' . "\n" .
                                 '  ON c.id=ccstm_t2.model_id AND ccstm_t2.var_id=\'' . $times['t2'] . '\' AND ccstm_t2.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2_from' . "\n" .
                                 '  ON c.id=ccstm_t2_from.model_id AND ccstm_t2_from.var_id=\'' . $times['t2_from'] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t2_to' . "\n" .
                                 '  ON c.id=ccstm_t2_to.model_id AND ccstm_t2_to.var_id=\'' . $times['t2_to'] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3' . "\n" .
                                 '  ON c.id=ccstm_t3.model_id AND ccstm_t3.var_id=\'' . $times['t3'] . '\' AND ccstm_t3.value IN (' . $doctor_workdays . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3_from' . "\n" .
                                 '  ON c.id=ccstm_t3_from.model_id AND ccstm_t3_from.var_id=\'' . $times['t3_from'] . '\'' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t3_to' . "\n" .
                                 '  ON c.id=ccstm_t3_to.model_id AND ccstm_t3_to.var_id=\'' . $times['t3_to'] . '\'' . "\n" .
                                 // config fields end
                                 'WHERE c.type=\'' . CUST_TYPE_DOCTOR . '\' AND c.active=1 AND c.deleted_by=0' . "\n" .
                                 '  AND ccstm_ray.num=' . $place . "\n" .
                                 '  AND (ccstm_t1.value IN (' . $doctor_workdays . ') OR ccstm_t2.value IN (' . $doctor_workdays . ') OR ccstm_t3.value IN (' . $doctor_workdays . ') OR (SELECT COUNT(ccstm_t0.model_id) FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_t0 WHERE ccstm_t0.model_id=c.id AND ccstm_t0.var_id IN (' . $doctor_no_data . '))=0)' . "\n" .
                                 ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                                 'GROUP BY c.id';
                        $targets = array_merge($targets, $db->GetAssoc($query));
                    }

                    if ($targets) {
                        // prepare text for duration from start until end of current period
                        $period_text = sprintf(self::$i18n->translate('reports_period_text'),
                                               General::strftime('%d.%m.%Y', $target_visit['period_from']),
                                               General::strftime('%d.%m.%Y', $target_visit['period_to']));

                        // get target visit grouping table
                        $target_visit_data = self::_getMeetingData($registry, $target_visit['id'], DOC_TYPE_TARGET_VISIT, $target_visit_vars);

                        // get target campaigns that overlap with period
                        $query = 'SELECT d.id' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_start' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_CAMPAIGN . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_start.model_id AND dcstm_start.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_START_PERIOD_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_finish' . "\n" .
                                 '  ON dcstm_start.model_id=dcstm_finish.model_id AND dcstm_finish.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_FINISH_PERIOD_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_start.value <= \'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND dcstm_finish.value >= \'' . $target_visit['period_from'] . '\'';
                        $target_campaigns = $db->GetCol($query);

                        // add campaign data to visit data
                        foreach ($target_campaigns as $doc_id) {
                            $target_campaign_data = self::_getMeetingData($registry, $doc_id, DOC_TYPE_TARGET_CAMPAIGN, $target_campaign_vars);
                            foreach ($target_campaign_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num;
                                } else {
                                    $target_visit_data[$key] = $num;
                                }
                            }
                        }

                        $target_ids = array();
                        foreach ($targets as $target) {
                            if (!in_array($target['id'], $target_ids)) {
                                $target_ids[] = $target['id'];
                            }
                        }

                        // last visit ever
                        $query = 'SELECT fir.customer, MAX(fir.issue_date) AS last_visited' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $last_visited = $db->GetAssoc($query);

                        // done visits within period that selected date is in
                        $query = 'SELECT fir.customer, COUNT(fir.id) AS done_visits' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.issue_date>=\'' . $target_visit['period_from'] . '\'' . "\n" .
                                 '  AND fir.issue_date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $done_visits = $db->GetAssoc($query);

                        // planned visits for date from tomorrow until end of current period
                        $planned_visits = array();
                        $tomorrow = date_add(new DateTime(), new DateInterval('P1D'))->format('Y-m-d');
                        if ($tomorrow <= $target_visit['period_to']) {
                            $query = 'SELECT gt2.article_id, COUNT(d.id)' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                     '  ON d.type=\'' . DOC_TYPE_DAILY_SCHEDULE . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                                     '    AND d.id=gt2.model_id AND gt2.model=\'Document\'' . "\n" .
                                     'WHERE d.date>=\'' . $tomorrow . '\' AND d.date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                     '  AND gt2.article_id IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                     'GROUP BY gt2.article_id';
                            $planned_visits = $db->GetAssoc($query);
                        }

                        $planned_visits_date = array();
                        if (!empty($filters['create_daily_schedule'])) {
                            // get employee of user
                            $query = 'SELECT employee FROM ' . DB_TABLE_USERS . ' WHERE id = \'' . $filters['agent'] . '\'';
                            $filters['agent'] = $db->GetOne($query);

                            // first check for existing daily schedules for agent and date
                            $query = 'SELECT d.id, dcstm.value' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm' . "\n" .
                                     '  ON d.type=\'' . DOC_TYPE_DAILY_SCHEDULE . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                                     '   AND d.id=dcstm.model_id' . "\n" .
                                     '   AND dcstm.var_id=\'' . $entities['daily_schedule']['vars'][DAILY_SCHEDULE_RAY_ID_VAR] . '\'' . "\n" .
                                     'WHERE d.date=\'' . $filters['date'] . '\' AND d.employee=\'' . $filters['agent'] . '\'' . "\n" .
                                     'ORDER BY d.id DESC';
                            $docs = $db->GetAssoc($query);

                            if ($docs) {
                                // if daily schedules exist for date and agent, new data will be added to latest one so
                                // get planned from that daily schedule (this is used to disable textbox for adding the same data again)
                                $first_doc_id = array_keys($docs);
                                $first_doc_id = reset($first_doc_id);
                                // get all targets, not only those among current results, in order to validate unique positions
                                $query = 'SELECT CONCAT(gt2.article_id, "_", gt2.free_field3) AS idx, gt2.free_field5 AS position' . "\n" .
                                         'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                         'WHERE gt2.model_id=\'' . $first_doc_id . '\' AND gt2.model=\'Document\'' . "\n" .
                                         //'  AND gt2.article_id IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                         'GROUP BY gt2.article_id, gt2.free_field3';
                                $planned_visits_date = $db->GetAssoc($query);

                                // flag that latest existing schedule is for another ray
                                if ($docs[$first_doc_id] != $filters['ray']) {
                                    $existing_schedule_another_ray = $first_doc_id;
                                }
                            }
                        }

                        $query = 'SELECT ct.model_id, MIN(ct.tag_id)' . "\n" .
                                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                                 'WHERE ct.model=\'Customer\' AND ct.model_id IN (\'' . implode('\', \'', $target_ids) . '\') AND ct.tag_id IN (' . CLASS_TAGS . ')' . "\n" .
                                 'GROUP BY ct.model_id';
                        $tags = $db->GetAssoc($query);

                        // group results by ray
                        foreach ($targets as $tidx => $res) {
                            if (!isset($final_results[$res['ray_id']])) {
                                $final_results[$res['ray_id']] = array(
                                    'targets' => array()
                                );
                            }

                            // customer id
                            $cid = $res['id'];

                            if (!array_key_exists($cid, $final_results[$res['ray_id']]['targets'])) {

                                $phone = $res['phone'] ? explode("\n", $res['phone']) : array();
                                $res['gsm'] = $res['gsm'] ? explode("\n", $res['gsm']) : array();
                                $phone = array_merge($phone, $res['gsm']);
                                foreach ($phone as $idx => $data) {
                                    if (preg_match('/\|/', $data)) {
                                        $phone[$idx] = preg_replace('/(\|.*)$/', '', $data);
                                    }
                                    $phone[$idx] = trim($phone[$idx]);
                                }
                                unset($res['gsm']);
                                $res['phone'] = $phone;

                                $res['tag'] = array_key_exists($cid, $tags) ? $tags[$cid] : '';
                                $res['class'] = array_key_exists($res['tag'], $class_tags) ? $class_tags[$res['tag']] : '-';

                                $key = $res['type'] . '_' . $res['tag'];
                                $res['target_visits'] = array_key_exists($key, $target_visit_data) ? $target_visit_data[$key] : 0;

                                $res['last_visited'] = array_key_exists($cid, $last_visited) ? $last_visited[$cid] : '';

                                $res['done_visits'] = array_key_exists($cid, $done_visits) ? $done_visits[$cid] : 0;

                                $res['planned_visits'] = array_key_exists($cid, $planned_visits) ? $planned_visits[$cid] : 0;

                                $final_results[$res['ray_id']]['targets'][$cid] = $res;
                            }

                            $res['worktime'] = sprintf('%s - %s', ($res['worktime'] ?: 'N/A'), ($res['worktime_to'] ?: 'N/A'));

                            // number of the workplace; pharmacies - 0, doctors - 1, 2 or 3
                            $tnum = intval(preg_replace('#(\d*)_?(\d*)#', '$2', $tidx));
                            $final_results[$res['ray_id']]['targets'][$cid]['locations'][] = array(
                                'num'      => $tnum,
                                'address'  => $res['address'],
                                'worktime' => $res['worktime'],
                                'planned'  => array_key_exists($cid . '_' . $tnum, $planned_visits_date),
                                'position' => (isset($planned_visits_date[$cid . '_' . $tnum]) ? $planned_visits_date[$cid . '_' . $tnum] : '')
                            );
                            unset($final_results[$res['ray_id']]['targets'][$cid]['address']);
                            unset($final_results[$res['ray_id']]['targets'][$cid]['worktime']);
                            unset($final_results[$res['ray_id']]['targets'][$cid]['worktime_to']);
                        }
                    }
                } else {
                    // search by period

                    $query = 'SELECT c.id AS idx, c.id, c.type, ci18n.name, ccstm_ray.value AS ray_id' . "\n" .
                             'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                             '  ON c.id=ci18n.parent_id AND ci18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm_ray' . "\n" .
                             '  ON c.id=ccstm_ray.model_id AND ccstm_ray.var_id IN (\'' . $pharmacy_vars[PHARMACY_RAY_ID_VAR] . '\', \'' . $doctor_vars[DOCTOR_RAY_ID_VAR] . '\')' . "\n" .
                             'WHERE c.type IN (\'' . CUST_TYPE_PHARMACY . '\', \'' . CUST_TYPE_DOCTOR . '\') AND c.active=1 AND c.deleted_by=0' . "\n" .
                             ($rays ? '  AND ccstm_ray.value IN (\'' . implode('\', \'', $rays) . '\')' . "\n" : '') .
                             'GROUP BY c.id';
                    $targets = $db->GetAssoc($query);

                    if ($targets) {

                        // build timeline
                        $timeline = array();
                        $timeline[$target_visit['from_date']] = $target_visit;

                        // get earliest target visit document
                        $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                 '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                 '  AND dcstm_fd.value <= \'' . $filters['date_from'] . '\'' . "\n" .
                                 'ORDER BY dcstm_fd.value DESC, d.id DESC' . "\n" .
                                 'LIMIT 1';
                        $first_target_visit = $db->GetRow($query);
                        if ($first_target_visit && $first_target_visit['id'] != $target_visit['id']) {
                            $timeline[$first_target_visit['from_date']] = $first_target_visit;
                        }

                        $in_between_target_visits = array();
                        if (!$first_target_visit || $first_target_visit['id'] != $target_visit['id']) {
                            $query = 'SELECT d.id, dcstm_fd.value AS from_date, CONVERT(dcstm_mn.value, SIGNED INT) AS months_num' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_fd' . "\n" .
                                     '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_VISIT . '\' AND d.status!="opened"' . "\n" .
                                     '    AND d.id=dcstm_fd.model_id AND dcstm_fd.var_id=\'' . $target_visit_vars[TARGET_VISIT_FROM_DATE_VAR] . '\'' . "\n" .
                                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_mn' . "\n" .
                                     '  ON dcstm_fd.model_id=dcstm_mn.model_id AND dcstm_mn.var_id=\'' . $target_visit_vars[TARGET_VISIT_MONTHS_NUM_VAR] . '\'' . "\n" .
                                     'WHERE dcstm_fd.value!="" AND CONVERT(dcstm_mn.value, SIGNED INT)>0' . "\n" .
                                     '  AND dcstm_fd.value > \'' . ($first_target_visit ? $first_target_visit['from_date'] : $filters['date_from']) . '\'' . "\n" .
                                     '  AND dcstm_fd.value < \'' . $target_visit['from_date'] . '\'' . "\n" .
                                     'ORDER BY dcstm_fd.value ASC, d.id DESC';
                            $in_between_target_visits = $db->GetAll($query);
                            foreach ($in_between_target_visits as $tv) {
                                if (!array_key_exists($tv['from_date'], $timeline)) {
                                    $timeline[$tv['from_date']] = $tv;
                                }
                            }
                        }
                        ksort($timeline);

                        $target_visit_data = array();
                        // get the start date of the visit period that 'date_from' is in
                        $start_of_first_period = '';
                        // calculate start and end of periods for selected period filter
                        $timeline = array_values($timeline);
                        $num_target_visits = count($timeline);

                        for ($i = 0; $i < $num_target_visits; $i++) {
                            $tv = &$timeline[$i];
                            $tv['period_from'] = $tv['from_date'];

                            // get first date of month for 'from_date' and calculate periods as if document always starts from there
                            $tv['next_period_from'] = date_add(
                                date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $tv['from_date'])),
                                new DateInterval('P' . $tv['months_num'] . 'M')
                            )->format('Y-m-d');

                            // count how many visit periods current document covers (within the searched dates)
                            $tv['period_count'] = 0;
                            if ($i > 0) {
                                $tv['period_count'] = 1;
                            } elseif ($tv['next_period_from'] > $filters['date_from']) {
                                $start_of_first_period = $tv['period_from'];
                                $tv['period_count'] = 1;
                            }

                            // figure out the last date that current target visit document is valid for
                            $tv_stop = isset($timeline[$i+1]) ?
                                       date_sub(date_create(preg_replace('/^(.*)\d{2}$/', '${1}01', $timeline[$i+1]['from_date'])), new DateInterval('P1D'))->format('Y-m-d') :
                                       $target_visit['period_to'];

                            while ($tv['next_period_from'] <= $tv_stop) {
                                $tv['period_from'] = $tv['next_period_from'];
                                $tv['next_period_from'] = date_add(date_create($tv['period_from']), new DateInterval('P' . $tv['months_num'] . 'M'))->format('Y-m-d');

                                if ($tv['next_period_from'] > $filters['date_from']) {
                                    $tv['period_count']++;
                                    if (!$i && !$start_of_first_period) {
                                        $start_of_first_period = $tv['period_from'];
                                    }
                                }
                            }
                            unset($tv['next_period_from']);

                            // get target visit grouping table
                            $tv_data = self::_getMeetingData($registry, $tv['id'], DOC_TYPE_TARGET_VISIT, $target_visit_vars);
                            foreach ($tv_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num * $tv['period_count'];
                                } else {
                                    $target_visit_data[$key] = $num * $tv['period_count'];
                                }
                            }
                        }

                        // get target campaigns that overlap with period
                        $query = 'SELECT d.id' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_start' . "\n" .
                                 '  ON d.active=1 AND d.deleted_by=0 AND d.type=\'' . DOC_TYPE_TARGET_CAMPAIGN . '\' AND d.status!="opened"' . "\n" .
                                 '    AND d.id=dcstm_start.model_id AND dcstm_start.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_START_PERIOD_VAR] . '\'' . "\n" .
                                 'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_finish' . "\n" .
                                 '  ON dcstm_start.model_id=dcstm_finish.model_id AND dcstm_finish.var_id=\'' . $target_campaign_vars[TARGET_CAMPAIGN_FINISH_PERIOD_VAR] . '\'' . "\n" .
                                 'WHERE dcstm_start.value <= \'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND dcstm_finish.value >= \'' . $start_of_first_period . '\'';
                        $target_campaigns = $db->GetCol($query);

                        // add campaign data to visit data
                        foreach ($target_campaigns as $doc_id) {
                            $target_campaign_data = self::_getMeetingData($registry, $doc_id, DOC_TYPE_TARGET_CAMPAIGN, $target_campaign_vars);
                            foreach ($target_campaign_data as $key => $num) {
                                if (array_key_exists($key, $target_visit_data)) {
                                    $target_visit_data[$key] += $num;
                                } else {
                                    $target_visit_data[$key] = $num;
                                }
                            }
                        }

                        // prepare text for duration from start of first period until end of last period
                        $period_text = sprintf(self::$i18n->translate('reports_period_text'),
                                               General::strftime('%d.%m.%Y', $start_of_first_period),
                                               General::strftime('%d.%m.%Y', $target_visit['period_to']));

                        // change filter values for displayed texts
                        if (!empty($filters['fix_period'])) {
                            unset($filters['fix_period']);
                            $filters['date_from'] = $start_of_first_period;
                            $filters['date_from_formatted'] = General::strftime('%d.%m.%Y', $filters['date_from']);
                            $filters['date_to'] = $target_visit['period_to'];
                            $filters['date_to_formatted'] = General::strftime('%d.%m.%Y', $filters['date_to']);
                            $session_param = 'reports_' . $filters['report_type'] . '_report';
                            $registry['session']->set($session_param, $filters, '', true);
                        }

                        $target_ids = array_keys($targets);

                        // last visit ever
                        $query = 'SELECT fir.customer, MAX(fir.issue_date) AS last_visited' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $last_visited = $db->GetAssoc($query);

                        // done visits within all periods overlapping with period filters
                        $query = 'SELECT fir.customer, COUNT(fir.id) AS done_visits' . "\n" .
                                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir ' . "\n" .
                                 'WHERE fir.type=\'' . FIR_TYPE_VISIT . '\' AND fir.active=1 AND fir.annulled_by=0' . "\n" .
                                 '  AND fir.customer IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                 '  AND fir.issue_date>=\'' . $start_of_first_period . '\'' . "\n" .
                                 '  AND fir.issue_date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                 '  AND fir.added_by IN (\'' . implode('\', \'', $sales_reps) . '\')' . "\n" .
                                 'GROUP BY fir.customer';
                        $done_visits = $db->GetAssoc($query);

                        // planned visits for date from tomorrow until end of current period
                        $planned_visits = array();
                        $tomorrow = date_add(new DateTime(), new DateInterval('P1D'))->format('Y-m-d');
                        if ($tomorrow <= $target_visit['period_to']) {
                            $query = 'SELECT gt2.article_id, COUNT(d.id)' . "\n" .
                                     'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                     '  ON d.type=\'' . DOC_TYPE_DAILY_SCHEDULE . '\' AND d.active=1 AND d.deleted_by=0' . "\n" .
                                     '    AND d.id=gt2.model_id AND gt2.model=\'Document\'' . "\n" .
                                     'WHERE d.date>=\'' . $tomorrow . '\' AND d.date<=\'' . $target_visit['period_to'] . '\'' . "\n" .
                                     '  AND gt2.article_id IN (\'' . implode('\', \'', $target_ids) . '\')' . "\n" .
                                     'GROUP BY gt2.article_id';
                            $planned_visits = $db->GetAssoc($query);
                        }

                        $query = 'SELECT ct.model_id, MIN(ct.tag_id)' . "\n" .
                                 'FROM ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                                 'WHERE ct.model=\'Customer\' AND ct.model_id IN (\'' . implode('\', \'', $target_ids) . '\') AND ct.tag_id IN (' . CLASS_TAGS . ')' . "\n" .
                                 'GROUP BY ct.model_id';
                        $tags = $db->GetAssoc($query);

                        // group results by ray
                        foreach ($targets as $cid => $res) {

                            if (!isset($final_results[$res['ray_id']])) {
                                $final_results[$res['ray_id']] = array(
                                    'targets' => array()
                                );
                            }

                            $res['tag'] = array_key_exists($cid, $tags) ? $tags[$cid] : '';
                            $res['class'] = array_key_exists($res['tag'], $class_tags) ? $class_tags[$res['tag']] : '-';

                            $key = $res['type'] . '_' . $res['tag'];
                            $res['target_visits'] = array_key_exists($key, $target_visit_data) ? $target_visit_data[$key] : 0;

                            $res['last_visited'] = array_key_exists($cid, $last_visited) ? $last_visited[$cid] : '';

                            $res['done_visits'] = array_key_exists($cid, $done_visits) ? $done_visits[$cid] : 0;

                            $res['planned_visits'] = array_key_exists($cid, $planned_visits) ? $planned_visits[$cid] : 0;

                            // dummy data just to comply with format of results when searching by date
                            $res['locations'] = array(array());

                            $final_results[$res['ray_id']]['targets'][$cid] = $res;
                        }
                    }
                }

                if ($final_results) {
                    // ray name and agent name
                    $query = 'SELECT n.id, IF(ni18n.name IS NOT NULL, ni18n.name, \'\') AS ray_name,' . "\n" .
                             'ncstm_agent.value AS agent_name' . "\n" .
                             'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                             '  ON n.id=ni18n.parent_id AND ni18n.lang=\'' . $model_lang . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_region' . "\n" .
                             '  ON n.id=ncstm_region.model_id AND ncstm_region.var_id=\'' . $entities['ray']['vars'][RAY_REGION_ID_VAR] . '\'' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_agent' . "\n" .
                             '  ON ncstm_region.value=ncstm_agent.model_id AND ncstm_agent.var_id=\'' . $entities['region']['vars'][REGION_AGENT_NAME_VAR] . '\'' . "\n" .
                             'WHERE n.id IN (\'' . implode('\', \'', array_keys($final_results)) . '\')';
                    $ray_data = $db->GetAssoc($query);

                    foreach ($final_results as $id => $ray) {
                        $ray_name = $agent_name = '';
                        if (array_key_exists($id, $ray_data)) {
                            $ray_name = $ray_data[$id]['ray_name'];
                            $agent_name = $ray_data[$id]['agent_name'];
                        }
                        $final_results[$id]['name'] = $ray_name;
                        $final_results[$id]['num'] = $ray_name ? preg_replace('/^.*\s(\d+).*/u', '$1', $ray_name) : '';
                        $final_results[$id]['agent_name'] = $agent_name;
                        $final_results[$id]['caption'] =
                            $filters['search_by'] == 1 ?
                            sprintf(self::$i18n->translate('reports_date_caption'),
                                General::strftime('%A, %d.%m.%Y', $filters['date']),
                                $ray_name ?: '-', $agent_name ?: '-') :
                            sprintf(self::$i18n->translate('reports_period_caption'),
                                General::strftime('%d.%m.%Y', $filters['date_from']),
                                General::strftime('%d.%m.%Y', $filters['date_to']),
                                $ray_name ?: '-', $agent_name ?: '-');
                        uasort($final_results[$id]['targets'], function ($a, $b) {
                            if (isset($a['locations'][0]['planned']) && isset($b['locations'][0]['planned'])) {
                                // filter only planned locations of targets and sort them by position (without casting to integers)
                                $a_planned = array_filter($a['locations'], function($ap) { return $ap['planned']; } );
                                $b_planned = array_filter($b['locations'], function($bp) { return $bp['planned']; } );
                                usort($a_planned, function($as, $bs) { return $as['position'] > $bs['position'] ? 1 : -1; });
                                usort($b_planned, function($as, $bs) { return $as['position'] > $bs['position'] ? 1 : -1; });

                                if ($a_planned && !$b_planned || !$a_planned && $b_planned) {
                                    // target with planned location should be first
                                    return $a_planned ? -1 : 1;
                                } elseif ($a_planned && $b_planned && $a_planned[0]['position'] !== $b_planned[0]['position']) {
                                    // if both are planned, order by lowest position
                                    return $a_planned[0]['position'] > $b_planned[0]['position'] ? 1 : -1;
                                }
                            }
                            // finally sort by name
                            return $a['name'] > $b['name'] ? 1 : -1;
                        } );
                    }
                    // sort results by agent name first and then by the number part of ray name
                    uasort($final_results, function ($a, $b) {
                        if ($a['agent_name'] > $b['agent_name']) {
                            return 1;
                        } elseif ($a['agent_name'] < $b['agent_name']) {
                            return -1;
                        } else {
                            return $a['num'] > $b['num'] ? 1 : -1;
                        }
                    } );
                }
            }

            // prepare all the additional options for report
            $final_results['additional_options'] = array();

            // hide export button
            $final_results['additional_options']['dont_show_export_button'] = true;

            if (!empty($filters['create_daily_schedule'])) {
                // existing positions in schedule (as integers) - needed for validation of entered positions
                $existing_positions = isset($planned_visits_date) ? array_values($planned_visits_date) : array();
                array_walk($existing_positions, function(&$a, $key) { $a = intval($a); });
                $final_results['additional_options']['existing_positions'] = json_encode($existing_positions);

                // display warning when schedule for another ray already exists for the same date and agent
                if (!empty($existing_schedule_another_ray)) {
                    $final_results['additional_options']['existing_schedule_another_ray'] = $existing_schedule_another_ray;
                }
            }

            // explanation text for span of period that data is searched within
            if (!empty($period_text)) {
                $final_results['additional_options']['period_text'] = $period_text;
            }

            // keep filter values in additional options because we need them in dashlet as well
            $final_results['additional_options']['filters'] = $filters;

            // prepare some data to be initially loaded in dashlet
            if ($registry['action'] == 'dashlet') {
                // sales reps should see only rays in their regions
                $ray_options = self::getRays($registry, $registry['currentUser']->get('id'));
                if (count ($ray_options) <= 1) {
                    $final_results['additional_options']['ray_options'] = $ray_options ? reset($ray_options) : array();
                } else {
                    $final_results['additional_options']['ray_optgroups'] = $ray_options;
                }

                // load report scripts
                /* $final_results['additional_options']['javascript'] = array(
                    'custom_scripts' => array(
                        array(
                            'type' => 'external',
                            'src' => PH_MODULES_URL . 'reports/plugins/' . $filters['report_type'] . '/javascript/custom.js'
                        )
                    )
                ); */
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }

        /**
         * Gets rays (of specified sales rep or all) as dropdown optgroups or gets just ids
         *
         * @param object $registry - the main registry
         * @param int $rep - user id to get rays in regions of specified sales rep, 0 - to get all rays
         * @param bool $get_options - get as dropdown optgroups or get just ids
         * @return array - found rays
         */
        public static function getRays(&$registry, $rep = 0, $get_options = true) {

            $lang = $registry['lang'];

            $sql_add_vars = 'SELECT name, id ' . "\n" .
                            'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                            'WHERE model="Nomenclature"' . "\n" .
                            '  AND (model_type="' . NOM_TYPE_RAY . '" AND name="' . RAY_REGION_ID_VAR . '"' . "\n" .
                            '    OR model_type="' . NOM_TYPE_REGION . '" AND name="' . REGION_AGENT_ID_VAR . '")';
            $add_vars = $registry['db']->GetAssoc($sql_add_vars);

            $region_id_var = isset($add_vars[RAY_REGION_ID_VAR]) ? $add_vars[RAY_REGION_ID_VAR] : 0;
            $agent_id_var = isset($add_vars[REGION_AGENT_ID_VAR]) ? $add_vars[REGION_AGENT_ID_VAR] : 0;

            $query = 'SELECT ' .
                     ($get_options ?
                     'IF(ni18n.name IS NOT NULL, ni18n.name, \'\') AS label, n.id AS option_value, ' . "\n" .
                     '  n.active AS active_option, n2i18n.name AS region_name, ' . "\n" .
                     '  CONCAT(\'user_\', ncstm_agent.value) AS class_name ' :
                     'n.id') . "\n" .
                     'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                     '  ON n.id=ni18n.parent_id AND ni18n.lang=\'' . $lang . '\'' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_region' . "\n" .
                     '  ON n.type=\'' . NOM_TYPE_RAY . '\' AND n.active=1' . "\n" .
                     '    AND n.id=ncstm_region.model_id AND ncstm_region.var_id=\'' . $region_id_var . '\'' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n2' . "\n" .
                     '  ON n2.type=\'' . NOM_TYPE_REGION . '\' AND n2.active=1' . "\n" .
                     '    AND ncstm_region.value=n2.id' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS ncstm_agent' . "\n" .
                     '  ON n2.id=ncstm_agent.model_id AND ncstm_agent.var_id=\'' . $agent_id_var . '\'' . "\n" .
                     ($rep > 0 ? '    AND ncstm_agent.value=\'' . $rep . '\'' . "\n" : '') .
                     ($get_options ?
                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS n2i18n' . "\n" .
                     '  ON n2.id=n2i18n.parent_id AND n2i18n.lang=\'' . $lang . '\'' . "\n" .
                     'ORDER BY n2.code ASC, REPLACE(label, \'Лъч \', \'\') ASC' :
                     '');

            if ($get_options) {
                $rays_options = $registry['db']->GetAll($query);

                $rays = array();
                foreach ($rays_options as $option) {
                    $region_name = $option['region_name'];
                    unset($option['region_name']);
                    if (!isset($rays[$region_name])) {
                        $rays[$region_name] = array();
                    }
                    $rays[$region_name][] = $option;
                }
            } else {
                $rays = $registry['db']->GetCol($query);
            }

            return $rays;
        }

        /**
         * Get grouping table data of target visit or target campaign document
         *
         * @param object $registry - the main registry
         * @param int $doc_id - document id
         * @param int $doc_type - document type
         * @param array $add_vars - pass data for additional vars if it is already available
         * @return array - result data
         */
        private static function _getMeetingData(&$registry, $doc_id, $doc_type, &$add_vars = array()) {
            if (!$doc_id || !in_array($doc_type, array(DOC_TYPE_TARGET_VISIT, DOC_TYPE_TARGET_CAMPAIGN))) {
                return array();
            }

            $prefix = ($doc_type == DOC_TYPE_TARGET_VISIT ? 'TARGET_VISIT' : 'TARGET_CAMPAIGN');
            $vars = array (
                'type' => constant($prefix . '_MEETINGS_CTYPE_VAR'),
                'tag' => constant($prefix . '_MEETINGS_CTAG_VAR'),
                'num' => constant($prefix . '_MEETINGS_NUM_VAR')
            );
            // get additional vars if not passed as param
            if (!$add_vars) {
                $sql_add_vars =
                    'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n" .
                    'WHERE model=\'Document\' AND model_type=\'' . $doc_type . '\' AND name IN (\'' . implode('\', \'', $vars) . '\')';
                $add_vars = $registry['db']->GetAssoc($sql_add_vars);
            }
            foreach ($vars as $alias => $var_name) {
                $vars[$alias] = array_key_exists($var_name, $add_vars) ? $add_vars[$var_name] : '0';
            }

            $query = 'SELECT CONCAT(dcstm_type.value, \'_\', dcstm_tag.value) AS idx, dcstm_num.value AS num' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_type' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_tag' . "\n" .
                     '  ON dcstm_type.model_id=\'' . $doc_id . '\'' . "\n" .
                     '    AND dcstm_type.model_id=dcstm_tag.model_id AND dcstm_type.num=dcstm_tag.num' . "\n" .
                     '    AND dcstm_type.var_id=\'' . $vars['type'] . '\' AND dcstm_tag.var_id=\'' . $vars['tag'] . '\'' . "\n" .
                     '    AND dcstm_type.value!=\'\' AND dcstm_tag.value!=\'\'' . "\n" .
                     'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_num' . "\n" .
                     '  ON dcstm_tag.model_id=dcstm_num.model_id AND dcstm_tag.num=dcstm_num.num' . "\n" .
                     '    AND dcstm_num.var_id=\'' . $vars['num'] . '\'' . "\n" .
                     'ORDER BY dcstm_type.num ASC';
            $meeting_rows = $registry['db']->GetAll($query);

            $meeting_data = array();
            // iterate all rows to get last row for each key
            foreach ($meeting_rows as $row) {
                $meeting_data[$row['idx']] = $row['num'];
            }

            return $meeting_data;
        }
    }

?>
