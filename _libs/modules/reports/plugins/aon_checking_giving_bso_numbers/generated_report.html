<table border="0" cellpadding="0" cellspacing="0" width="100%">
  <tr>
    <td>
      {if $reports_additional_options.type_table eq 'give_bso'}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row hcenter">
            <td class="t_border" style="vertical-align: middle;{if $reports_additional_options.hide_car_column} display: none;{/if}">{#reports_car#|escape}</td>
            <td class="t_border" style="vertical-align: middle;">{#reports_type_blank#|escape}</td>
            <td style="vertical-align: middle;">{#reports_for_record#|escape}</td>
          </tr>
          {foreach from=$reports_results item=result name=results}
            {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
            <tr class="{$current_row_class}">
              <td class="t_border" rowspan="{$result.rowspan}" style="vertical-align: middle;{if $reports_additional_options.hide_car_column} display: none;{/if}">
                {$result.name|escape|default:"&nbsp;"}
              </td>
              {foreach from=$result.blank_types item=blank_type name=bt}
                {if !$smarty.foreach.bt.first}
                  <tr class="{$current_row_class}">
                {/if}
                <td class="t_border">
                  {$blank_type.label|escape|default:"&nbsp;"}
                </td>
                <td>
                  {include file="input_autocompleter.html"
                    var=$blank_type.autocompleter
                    width=222
                    standalone=true
                    name=$blank_type.autocompleter.name
                    custom_id=$blank_type.autocompleter.custom_id
                    label=$blank_type.autocompleter.label
                    help=$blank_type.autocompleter.help
                    value=$blank_type.autocompleter.value
                    value_autocomplete=$option.value_autocomplete
                    autocomplete=$blank_type.autocompleter.autocomplete
                    autocomplete_var_type='basic'
                    autocomplete_type=$blank_type.autocompleter.autocomplete_type
                    autocomplete_buttons = $blank_type.autocompleter.autocomplete_buttons
                  }
                </td>
                {if !$smarty.foreach.bt.first}
                  </tr>
                {/if}
              {foreachelse}
                <td class="error" colspan="2">{#reports_no_blanks#|escape}</td>
              {/foreach}
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="3">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="3"></td>
          </tr>
        </table>
        <br />
        <input type="hidden" value="{$reports_additional_options.contract_id}" name="contract_id" id="contract_id">
        <button type="submit" name="fillBsoButton" class="button" onclick="return checkCompletedRecords(this);">{#reports_give_bso_number#|escape}</button>
      {else}
        <table border="0" cellpadding="5" cellspacing="0" class="t_table t_list">
          <tr class="reports_title_row hcenter">
            <td class="t_border" style="vertical-align: middle;"><div style="width: 115px;">{#reports_blank_type#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_protocol_num#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 90px;">{#reports_protocol_date#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;">{#reports_num#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;">{#reports_insurer#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;">{#reports_policy_num#|escape}</div></td>
            <td class="t_border" style="vertical-align: middle;"><div style="width: 140px;">{#reports_client#|escape}</div></td>
            <td style="vertical-align: middle;"><div style="width: 90px;">{#reports_nom_status#|escape}</div></td>
          </tr>
          {foreach from=$reports_results item=result name=results}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="t_border" style="background-color: #{$result.row_color};" width="119">
                {$result.blank_type_name|escape|default:"&nbsp;"}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="94">
                {if $result.protocol_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=documents&amp;documents=view&amp;view={$result.protocol_id}">{$result.protocol_num|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="94">
                {if $result.protocol_date}{$result.protocol_date|date_format:#date_short#|escape|default:"&nbsp;"}{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="144">
                {if $result.id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=nomenclatures&amp;nomenclatures=view&amp;view={$result.id}">{$result.num|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="144">
                {if $result.insurer_name}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.insurer}">{$result.insurer_name|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="144">
                {if $result.policy_id}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=contracts&amp;contracts=view&amp;view={$result.policy_id}">{$result.policy_num|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
              </td>
              <td class="t_border" style="background-color: #{$result.row_color};" width="144">
                {if $result.client_name}<a href="{$smarty.server.SCRIPT_NAME}?{$module_param}=customers&amp;customers=view&amp;view={$result.client}">{$result.client_name|escape|default:"&nbsp;"}</a>{else}&nbsp;{/if}
              </td>
              <td style="background-color: #{$result.row_color};" width="94">
                {$result.status_name|escape|default:"&nbsp;"}
              </td>
            </tr>
          {foreachelse}
            <tr class="{cycle values='t_odd,t_even'}">
              <td class="error" colspan="8">{#no_items_found#|escape}</td>
            </tr>
          {/foreach}
          <tr>
            <td class="t_footer" colspan="8"></td>
          </tr>
        </table>
      {/if}
    </td>
  </tr>
</table>