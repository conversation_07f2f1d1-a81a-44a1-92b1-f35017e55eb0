filter_report_for = Report for
filter_report_for_create_orders_to_supplier = Create Orders to Supplier
filter_report_for_planning_and_informing_supplier = Final Confirmation for Loading Week
filter_report_for_tlr = Truck Planning
filter_trading_company = Trading Company
filter_point_of_sale = Point of Sell
filter_logistics_specialist = Logistics Specialist
filter_ls_full_num = Logistics Specification
filter_ls_status = Logistics Specification Status
filter_period_specification = Period Specification (customer/internal)
filter_specification_status = Specification Status
filter_specification_status_not_processed = Not Processed
filter_specification_status_no_documents = No Documents
filter_specification_status_no_payment_from_customer = No Payment from Customer
filter_specification_status_ready_to_order = Ready to Order
filter_specification_status_released = Released
filter_specification_confirmed_from_supplier_pending_payment = Confirmed from Supplier / Pending Payment
filter_include_closed_specifications = Include Closed Specifications
filter_customer = Customer
filter_product_category = Product Category
filter_product_in_specification = Product
filter_supplier_type = Supplier Type
filter_supplier_type_products = Products
filter_supplier_type_electrical_appliances = Electrical Appliances
filter_supplier = Supplier
filter_period_os = Period Order to Supplier
filter_os_status = Order to Supplier Status
filter_include_closed_orders = Include Closed Orders
filter_os_full_num = Order to Supplier
filter_country = Country
filter_allocation_status = Allocation Status
filter_allocation_status_unallocated_without_confirmed_date = Unallocated (without confirmed date)
filter_allocation_status_unallocated_with_confirmed_date = Unallocated (with confirmed date)
filter_allocation_status_allocated = Allocated
filter_requested_week = Requested Week
filter_requested_week_week = Week
filter_requested_week_year = Year
filter_confirmed_week = Confirmed Week
filter_confirmed_week_week = Week
filter_confirmed_week_year = Year

th_supplier_information = Supplier Information
th_trading_company = Trading Company
th_point_of_sell = Point of Sell
th_logistics_specification = Logistics Specification
th_connected_documents_files = Connected Documents, Files
th_customer_specification_files_alt = CS Files
th_customer_specification_files_title = Customer Specification Files
th_customer_contract_files_alt = CC Terms
th_customer_contract_files_title = Customer Contract Signed Terms
th_allow_os_condition = Customer Contract Sales Conditions
th_allow_os_tags = Customer Contract Tags Allow Adding of Order to Supplier
th_payments = Payments
th_product_nzoom_code = Product nZoom Code
th_notification_to_supplier = Notification to Supplier
th_product_supplier_code = Supplier Product Code
th_product_name = Product Name
th_product_description = Material
th_product_category = Product Category
th_element_supplier_code = Element Supplier Code
th_element_name = Element Name
th_product_model = Product Model
th_color_code_material = Customer Description
th_element_size = Size
th_measure = Unit
th_public_price = List Price (%s)
th_standard_supplier_discount = % Standard Supplier Discount
th_net_price = Net Price (%s)
th_quantity = Quantity
th_total_list_price = Total List Price (%s)
th_total_amount = Total Net Price (%s)
th_lead_time = Lead Time (weeks)
th_order_to_supplier = Order to Supplier
th_requested_week = Requested Week
th_request_date = Request Production Date/Week
th_request_date_os = Request Production Date/Week
th_loading_arriving_at_warehouse = Loading/arriving at warehouse
th_final_delivery_week = Final Delivery Week
th_as_per_customer_contract = As per customer contract
th_remaining_weeks = Remaining Weeks
th_from_now_until_delivery = From Now Until Delivery
th_terms_of_purchase = Terms of Purchase
th_invoices = Invoices
th_invoices_total_amount = Total Amount
th_invoices_total_paid_amount = Total Paid Amount
th_invoices_for_payment_amount = For Payment Amount
th_arp = Accounting Request for Payments
th_total_kg = Total KG
th_total_volume = Total Volume
th_connected_documents = Connected Documents
th_weight = KG
th_volume = Volume
th_confirmed_correct_date = Confirm /Correct Loading Date
has_tlr_invoices_closed = Has TLR / Invoices / Closed
toolbar_request_date_label = Request Production Date
toolbar_request_date_title = Request Production Date (Loading/arriving at warehouse)

report_tlr = Truck Loading Request

type_code_name_invoice = I
type_code_name_proforma_invoice = PI

no_valid_rate_found = No valid exchange rate found from %s to %s! Rate %d was used in the calculations.

button_create_orders_to_supplier = Create Orders to Supplier
loading_date_title = Loading Date

add_arps_save_confirmed_dates = Create / Save
add_tlr = Create <br /> TLR
add_tlr_header = Create Truck Loading Request
add_tlr_save = Save
add_tlr_cancel = Cancel
add_tlr_ms_error = Please, fill required fields!
remove_from_tlr = Remove from <br /> TLR
are_you_sure_to_remove_from_tlr = Are you sure you want to remove the selected rows from their Truck Loading Request?
remove_from_tlr_yes = Yes
remove_from_tlr_no = No
add_to_existing_tlr = Add to existing <br /> TLR
add_to_existing_tlr_header = Add to existing Truck Loading Request
add_to_existing_tlr_save = Save
add_to_existing_tlr_cancel = Cancel
save_confirmed_dates = Save <br /> Confirmed Dates

failed_to_add_tlr = Failed to add Truck Loading Request!
successfully_added_tlr = Successfully added Truck Loading Request: %s
failed_to_remove_rows_from_tlr = Failed to remove rows from Truck Loading Request!
successfully_removed_rows_from_tlr = Successfully removed rows from Truck Loading Request!
failed_to_add_to_existing_tlr = Failed to add rows to existing Truck Loading Request!
successfully_add_to_existing_tlr = Successfully added rows to existing Truck Loading Request!
failed_to_add_orders = Failed to add orders!
successfully_added_orders = Successfully added orders: %s
failed_to_add_arps = Failed to add requests!
successfully_added_arps = Successfully added requests: %s
successfully_added_notifications_to_suppliers = Successfully added notifications to suppliers: %s
successfully_confirmed_corrected_dates = You successfully confirmed/corrected dates!
please_select_elements = Please select articles!
please_fill_loading_date = Please fill Request Date!
could_not_find_data_for_orders = Could not find data for orders!
please_fill_some_data = Please fill some data to be saved!
save_confirmed_dates_success = Confirmed dates saved successfully!

legend_caption = Legend:
legend_os_done = Fully Ordered
legend_os_partially = Partially Ordered
legend_os_unable = Cannot be Ordered

th_week_truck = Week Truck
week_truck_group_caption_week = Week
week_truck_group_caption_unallocated = Unallocated

truck = Truck
report_current_week = Current Week
th_confirmed_date = Confirm /Correct Loading Date
supplier_loading_point_address = Address
supplier_loading_point_sequence = Sequence

own_company = Trading Company
loading_company = Transport Company
logistics_company_agent = Logistics Company Agent
delivery_warehouse = Delivery Warehouse

totals_of_selected = Selected

designer = Designer
other_designer = Other Designer
ls_trading_company = Customer
cc_trading_company = Customer As Per Contract
is_trading_company = Customer

payment_deadline = Payment Deadline

msg_arp_no_changes_to_save = There are no changes to save!
msg_arp_empty_payment_deadlines = There are checked rows with empty payment deadline!

tlr_combined = combined

week_formatted = Week %s (%s)

expand_all = Expand All
collapse_all = Collapse All

column_chooser_text = Columns

no_invoice = no invoice
