a:6:{s:9:"classesIn";a:1:{s:24:"Nzoom\Export\DataFactory";a:6:{s:4:"name";s:11:"DataFactory";s:14:"namespacedName";s:24:"Nzoom\Export\DataFactory";s:9:"namespace";s:12:"Nzoom\Export";s:9:"startLine";i:17;s:7:"endLine";i:511;s:7:"methods";a:17:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:31:"__construct(Registry $registry)";s:10:"visibility";s:6:"public";s:9:"startLine";i:44;s:7:"endLine";i:47;s:3:"ccn";i:1;}s:16:"setTableProvider";a:6:{s:10:"methodName";s:16:"setTableProvider";s:9:"signature";s:90:"setTableProvider(?Nzoom\Export\Provider\ExportTableProviderInterface $tableProvider): void";s:10:"visibility";s:6:"public";s:9:"startLine";i:54;s:7:"endLine";i:57;s:3:"ccn";i:1;}s:15:"isTablesEnabled";a:6:{s:10:"methodName";s:15:"isTablesEnabled";s:9:"signature";s:23:"isTablesEnabled(): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:64;s:7:"endLine";i:67;s:3:"ccn";i:1;}s:22:"withModelTableProvider";a:6:{s:10:"methodName";s:22:"withModelTableProvider";s:9:"signature";s:103:"withModelTableProvider(string $referenceColumnName, string $referenceColumnLabel, array $options): self";s:10:"visibility";s:6:"public";s:9:"startLine";i:77;s:7:"endLine";i:89;s:3:"ccn";i:1;}s:8:"__invoke";a:6:{s:10:"methodName";s:8:"__invoke";s:9:"signature";s:73:"__invoke(array $models, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:98;s:7:"endLine";i:125;s:3:"ccn";i:3;}s:23:"createHeaderFromOutlook";a:6:{s:10:"methodName";s:23:"createHeaderFromOutlook";s:9:"signature";s:75:"createHeaderFromOutlook(Outlook $outlook): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:133;s:7:"endLine";i:159;s:3:"ccn";i:6;}s:18:"processModelsChunk";a:6:{s:10:"methodName";s:18:"processModelsChunk";s:9:"signature";s:125:"processModelsChunk(array $models, Nzoom\Export\Entity\ExportData $exportData, Nzoom\Export\Entity\ExportHeader $header): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:168;s:7:"endLine";i:173;s:3:"ccn";i:2;}s:21:"createRecordFromModel";a:6:{s:10:"methodName";s:21:"createRecordFromModel";s:9:"signature";s:111:"createRecordFromModel(Model $model, Nzoom\Export\Entity\ExportHeader $header): Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:182;s:7:"endLine";i:225;s:3:"ccn";i:8;}s:22:"getModelReferenceValue";a:6:{s:10:"methodName";s:22:"getModelReferenceValue";s:9:"signature";s:45:"getModelReferenceValue(Model $model): ?string";s:10:"visibility";s:7:"private";s:9:"startLine";i:233;s:7:"endLine";i:257;s:3:"ccn";i:5;}s:22:"extractTablesForRecord";a:6:{s:10:"methodName";s:22:"extractTablesForRecord";s:9:"signature";s:84:"extractTablesForRecord(Nzoom\Export\Entity\ExportRecord $record, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:265;s:7:"endLine";i:281;s:3:"ccn";i:5;}s:24:"mapFieldTypeToExportType";a:6:{s:10:"methodName";s:24:"mapFieldTypeToExportType";s:9:"signature";s:51:"mapFieldTypeToExportType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:289;s:7:"endLine";i:311;s:3:"ccn";i:1;}s:12:"setChunkSize";a:6:{s:10:"methodName";s:12:"setChunkSize";s:9:"signature";s:28:"setChunkSize(int $chunkSize)";s:10:"visibility";s:6:"public";s:9:"startLine";i:318;s:7:"endLine";i:321;s:3:"ccn";i:1;}s:15:"createStreaming";a:6:{s:10:"methodName";s:15:"createStreaming";s:9:"signature";s:118:"createStreaming(string $factoryClass, array $filters, Outlook $outlook, int $pageSize): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:332;s:7:"endLine";i:372;s:3:"ccn";i:2;}s:21:"createCursorStreaming";a:6:{s:10:"methodName";s:21:"createCursorStreaming";s:9:"signature";s:143:"createCursorStreaming(string $modelClass, array $filters, Outlook $outlook, int $pageSize, string $cursorField): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:384;s:7:"endLine";i:443;s:3:"ccn";i:4;}s:32:"extractModelTypeFromFactoryClass";a:6:{s:10:"methodName";s:32:"extractModelTypeFromFactoryClass";s:9:"signature";s:62:"extractModelTypeFromFactoryClass(string $factoryClass): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:452;s:7:"endLine";i:459;s:3:"ccn";i:1;}s:25:"extractModelTypeFromModel";a:6:{s:10:"methodName";s:25:"extractModelTypeFromModel";s:9:"signature";s:47:"extractModelTypeFromModel(Model $model): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:467;s:7:"endLine";i:487;s:3:"ccn";i:2;}s:11:"createEmpty";a:6:{s:10:"methodName";s:11:"createEmpty";s:9:"signature";s:80:"createEmpty(string $modelType, Outlook $outlook): Nzoom\Export\Entity\ExportData";s:10:"visibility";s:6:"public";s:9:"startLine";i:497;s:7:"endLine";i:510;s:3:"ccn";i:1;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:512;s:18:"commentLinesOfCode";i:176;s:21:"nonCommentLinesOfCode";i:336;}s:15:"ignoredLinesFor";a:1:{i:0;i:17;}s:17:"executableLinesIn";a:163:{i:46;i:5;i:56;i:6;i:66;i:7;i:77;i:8;i:79;i:9;i:82;i:10;i:83;i:10;i:84;i:10;i:85;i:10;i:87;i:11;i:88;i:12;i:101;i:13;i:102;i:14;i:103;i:15;i:104;i:16;i:108;i:17;i:111;i:18;i:112;i:18;i:113;i:18;i:114;i:18;i:119;i:19;i:120;i:20;i:121;i:21;i:124;i:22;i:135;i:23;i:136;i:24;i:138;i:25;i:139;i:26;i:141;i:27;i:143;i:28;i:145;i:29;i:146;i:30;i:148;i:31;i:149;i:31;i:150;i:31;i:151;i:31;i:152;i:31;i:153;i:31;i:154;i:31;i:158;i:32;i:170;i:33;i:171;i:34;i:184;i:35;i:185;i:35;i:186;i:35;i:189;i:36;i:190;i:37;i:191;i:38;i:192;i:39;i:196;i:40;i:198;i:41;i:199;i:42;i:202;i:43;i:205;i:44;i:206;i:45;i:209;i:46;i:210;i:47;i:211;i:48;i:212;i:49;i:216;i:50;i:220;i:51;i:221;i:52;i:224;i:53;i:235;i:54;i:236;i:55;i:239;i:56;i:242;i:57;i:243;i:58;i:244;i:59;i:248;i:60;i:249;i:61;i:250;i:62;i:251;i:63;i:252;i:64;i:256;i:65;i:268;i:66;i:270;i:67;i:271;i:68;i:273;i:69;i:275;i:70;i:276;i:71;i:277;i:71;i:278;i:71;i:291;i:72;i:292;i:72;i:293;i:72;i:294;i:72;i:295;i:72;i:296;i:72;i:297;i:72;i:298;i:72;i:299;i:72;i:300;i:72;i:301;i:72;i:302;i:72;i:303;i:72;i:304;i:72;i:305;i:72;i:306;i:72;i:307;i:72;i:308;i:72;i:310;i:73;i:320;i:74;i:335;i:75;i:338;i:76;i:341;i:77;i:342;i:77;i:343;i:77;i:344;i:77;i:349;i:78;i:366;i:78;i:351;i:79;i:352;i:79;i:353;i:79;i:354;i:79;i:357;i:80;i:360;i:81;i:361;i:82;i:362;i:83;i:365;i:84;i:369;i:85;i:371;i:86;i:387;i:87;i:390;i:88;i:393;i:89;i:394;i:89;i:395;i:89;i:396;i:89;i:401;i:90;i:404;i:91;i:437;i:91;i:406;i:92;i:409;i:93;i:410;i:94;i:411;i:95;i:412;i:96;i:416;i:97;i:417;i:98;i:420;i:99;i:422;i:100;i:423;i:101;i:427;i:102;i:428;i:103;i:431;i:104;i:432;i:105;i:433;i:106;i:436;i:107;i:440;i:108;i:442;i:109;i:455;i:110;i:458;i:111;i:470;i:112;i:473;i:113;i:481;i:114;i:482;i:115;i:486;i:116;i:500;i:117;i:503;i:118;i:504;i:118;i:505;i:118;i:506;i:118;i:507;i:118;i:509;i:119;}}