<?php /* Smarty version 2.6.33, created on 2025-05-27 12:01:33
         compiled from input_checkbox_group.html */ ?>
<?php require_once(SMARTY_CORE_DIR . 'core.load_plugins.php');
smarty_core_load_plugins(array('plugins' => array(array('modifier', 'default', 'input_checkbox_group.html', 66, false),array('modifier', 'escape', 'input_checkbox_group.html', 66, false),array('modifier', 'strip_tags', 'input_checkbox_group.html', 66, false),array('modifier', 'count', 'input_checkbox_group.html', 99, false),array('modifier', 'mb_truncate', 'input_checkbox_group.html', 99, false),array('function', 'help', 'input_checkbox_group.html', 81, false),array('function', 'counter', 'input_checkbox_group.html', 123, false),)), $this); ?>
<?php if ($this->_tpl_vars['index'] && $this->_tpl_vars['index']/1): ?><?php echo ''; ?><?php $this->assign('group_index', 1); ?><?php echo ''; ?><?php ob_start(); ?><?php echo ''; ?><?php if ($this->_tpl_vars['eq_indexes']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']; ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['empty_indexes']): ?><?php echo ''; ?><?php elseif ($this->_tpl_vars['name_index']): ?><?php echo ''; ?><?php echo $this->_tpl_vars['name_index']; ?><?php echo ''; ?><?php else: ?><?php echo ''; ?><?php echo $this->_tpl_vars['index']-1; ?><?php echo ''; ?><?php endif; ?><?php echo ''; ?><?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('index_array', ob_get_contents());ob_end_clean(); ?><?php echo ''; ?>
<?php endif; ?>

<?php $this->assign('options_count', 0); ?>
<?php $this->assign('options_html', ''); ?>

<?php if (! empty ( $this->_tpl_vars['options'] )): ?>
  <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?>
    <?php if (( ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] ) )): ?>
      <?php $this->assign('options_count', $this->_tpl_vars['options_count']+1); ?>
      <?php if (! isset ( $this->_tpl_vars['first_visible_option_label'] )): ?>
        <?php $this->assign('first_visible_option_label', $this->_tpl_vars['option']['label']); ?>
      <?php endif; ?>
      <?php ob_start(); ?>
        <?php echo $this->_tpl_vars['options_html']; ?>

        <input type="checkbox" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>[]" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['cb']['iteration']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if (is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] )): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?><?php if ($this->_tpl_vars['readonly']): ?> onclick="deselectCheckboxes(this)"<?php endif; ?><?php if ($this->_tpl_vars['custom_class'] || $this->_tpl_vars['option']['class_name']): ?> class="<?php echo $this->_tpl_vars['custom_class']; ?>
 <?php echo $this->_tpl_vars['option']['class_name']; ?>
"<?php endif; ?> /><label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['cb']['iteration']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" class="<?php if ($this->_tpl_vars['option']['class_name']): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
 <?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>inactive_option<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?> title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>* <?php endif; ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></label><?php if ($this->_tpl_vars['options_align'] != 'horizontal' && ! ($this->_foreach['cb']['iteration'] == $this->_foreach['cb']['total'])): ?><br /><?php endif; ?>
      <?php $this->_smarty_vars['capture']['default'] = ob_get_contents();  $this->assign('options_html', ob_get_contents());ob_end_clean(); ?>
    <?php endif; ?>
  <?php endforeach; endif; unset($_from); ?>
<?php endif; ?>

<?php if (! $this->_tpl_vars['standalone']): ?>
<tr<?php if ($this->_tpl_vars['hidden']): ?> style="display: none"<?php endif; ?>>
    <td class="labelbox">
        <a name="error_<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"></a>
            <?php if (! ( $this->_tpl_vars['options_count'] == 1 && $this->_tpl_vars['label'] == $this->_tpl_vars['first_visible_option_label'] )): ?>
    <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
"<?php if ($this->_tpl_vars['messages']->getErrors($this->_tpl_vars['name'])): ?> class="error"<?php endif; ?>><?php echo smarty_function_help(array('label_content' => $this->_tpl_vars['label'],'text_content' => $this->_tpl_vars['help']), $this);?>
</label>
    <?php endif; ?>
  </td>

    <td<?php if ($this->_tpl_vars['required']): ?> class="required"><?php echo $this->_config[0]['vars']['required']; ?>
<?php else: ?> class="unrequired">&nbsp;<?php endif; ?></td>

    <td nowrap="nowrap">
<?php endif; ?>

  <div style="white-space: normal;">
        <?php if ($this->_tpl_vars['optgroups']): ?>
      <div class="m_header_menu">
        <ul style="float: left; padding: 0 5px;">
        <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['circle'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['circle']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['options']):
        $this->_foreach['circle']['iteration']++;
?>
            <li id="tab_<?php echo $this->_tpl_vars['index']; ?>
<?php echo $this->_foreach['circle']['iteration']; ?>
" title="<?php echo ((is_array($_tmp=$this->_tpl_vars['key'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
">
              <span<?php if (empty ( $this->_tpl_vars['selected_tab'] ) && ($this->_foreach['circle']['iteration'] <= 1) || $this->_tpl_vars['selected_tab'] == $this->_tpl_vars['key']): ?> class="selected"<?php endif; ?>><a onclick="toggleTabs(this);"><?php if (count($this->_tpl_vars['optgroups']) > 5): ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['key'])) ? $this->_run_mod_handler('mb_truncate', true, $_tmp, 15, '...', false) : smarty_modifier_mb_truncate($_tmp, 15, '...', false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php else: ?><?php echo ((is_array($_tmp=$this->_tpl_vars['key'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
<?php endif; ?></a></span>
            </li>
        <?php endforeach; endif; unset($_from); ?>
        </ul>
        <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['circle'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['circle']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['options']):
        $this->_foreach['circle']['iteration']++;
?>
        <div style="float: right;<?php if (! ( empty ( $this->_tpl_vars['selected_tab'] ) && ($this->_foreach['circle']['iteration'] <= 1) || $this->_tpl_vars['selected_tab'] == $this->_tpl_vars['key'] )): ?> display: none<?php endif; ?>" id="tab_toggler_<?php echo $this->_tpl_vars['index']; ?>
<?php echo $this->_foreach['circle']['iteration']; ?>
">
          <?php if (! $this->_tpl_vars['do_not_show_check_all_button']): ?>
            <span onclick="toggleCheckboxes(this, '<?php echo $this->_tpl_vars['name']; ?>
', true, 'tab_container_<?php echo $this->_tpl_vars['index']; ?>
<?php echo $this->_foreach['circle']['iteration']; ?>
')" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
          <?php endif; ?>
          <?php if (! $this->_tpl_vars['do_not_show_check_all_button'] && ! $this->_tpl_vars['do_not_show_check_none_button']): ?>|<?php endif; ?>
          <?php if (! $this->_tpl_vars['do_not_show_check_none_button']): ?>
            <span onclick="toggleCheckboxes(this, '<?php echo $this->_tpl_vars['name']; ?>
', false, 'tab_container_<?php echo $this->_tpl_vars['index']; ?>
<?php echo $this->_foreach['circle']['iteration']; ?>
')" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
          <?php endif; ?>
        </div>
        <?php endforeach; endif; unset($_from); ?>
      </div>
      <div class="m_header_m_menu scroll_box_container" style="margin-top: 19px;">
      <?php if ($this->_tpl_vars['height']): ?>
        <?php $this->assign('height_is_custom', '1'); ?>
      <?php endif; ?>
      <?php $_from = $this->_tpl_vars['optgroups']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['circle'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['circle']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['key'] => $this->_tpl_vars['options']):
        $this->_foreach['circle']['iteration']++;
?>
       <div class="scroll_box_checkboxes<?php if (! $this->_tpl_vars['height_is_custom']): ?> scroll_box_checkboxes_height<?php endif; ?>" id="tab_container_<?php echo $this->_tpl_vars['index']; ?>
<?php echo $this->_foreach['circle']['iteration']; ?>
" style="<?php if (! ( empty ( $this->_tpl_vars['selected_tab'] ) && ($this->_foreach['circle']['iteration'] <= 1) || $this->_tpl_vars['selected_tab'] == $this->_tpl_vars['key'] )): ?>display: none;<?php endif; ?><?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
px;<?php endif; ?>">
          <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?>
            <?php if (( ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] ) )): ?>
              <input type="checkbox" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>[]" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo smarty_function_counter(array('name' => 'counts','skip' => 0), $this);?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if (is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] )): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?><?php if ($this->_tpl_vars['readonly']): ?> onclick="deselectCheckboxes(this)"<?php endif; ?><?php if ($this->_tpl_vars['custom_class'] || $this->_tpl_vars['option']['class_name']): ?> class="<?php echo $this->_tpl_vars['custom_class']; ?>
 <?php echo $this->_tpl_vars['option']['class_name']; ?>
"<?php endif; ?> /> <label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo smarty_function_counter(array('name' => 'counts','skip' => 1), $this);?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" class="<?php if ($this->_tpl_vars['option']['class_name']): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
 <?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>inactive_option<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?> title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>* <?php endif; ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></label><?php if (! ($this->_foreach['cb']['iteration'] == $this->_foreach['cb']['total'])): ?><br /><?php endif; ?>
            <?php endif; ?>
          <?php endforeach; endif; unset($_from); ?>
        </div>
      <?php endforeach; endif; unset($_from); ?>
      </div>
    <?php else: ?>
      <?php if ($this->_tpl_vars['options'] && $this->_tpl_vars['options_count'] > 10): ?>
        <?php if (! $this->_tpl_vars['do_not_show_check_all_button']): ?>
          <span onclick="toggleCheckboxes(this, '<?php echo $this->_tpl_vars['name']; ?>
', true)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_all'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        <?php endif; ?>
        <?php if (! $this->_tpl_vars['do_not_show_check_all_button'] && ! $this->_tpl_vars['do_not_show_check_none_button']): ?>|<?php endif; ?>
        <?php if (! $this->_tpl_vars['do_not_show_check_none_button']): ?>
          <span onclick="toggleCheckboxes(this, '<?php echo $this->_tpl_vars['name']; ?>
', false)" class="pointer"><?php echo ((is_array($_tmp=$this->_config[0]['vars']['check_none'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
</span>
        <?php endif; ?>
        <?php if (! $this->_tpl_vars['do_not_show_check_all_button'] || ! $this->_tpl_vars['do_not_show_check_none_button']): ?><br /><?php endif; ?>
      <?php endif; ?>
      <?php if ($this->_tpl_vars['options_count'] > 10): ?><div class="scroll_box" style="<?php if ($this->_tpl_vars['height']): ?>height: <?php echo $this->_tpl_vars['height']; ?>
px;<?php endif; ?>"><?php endif; ?>
        <?php if (! $this->_tpl_vars['standalone']): ?>
        <table border="0" cellpadding="0" cellspacing="0" class="nz-checkboxgroup-tbl">
          <tr>
            <td valign="top" class="nopadding">
        <?php endif; ?>
            <?php $_from = $this->_tpl_vars['options']; if (($_from instanceof StdClass) || (!is_array($_from) && !is_object($_from))) { settype($_from, 'array'); }$this->_foreach['cb'] = array('total' => count($_from), 'iteration' => 0);
if ($this->_foreach['cb']['total'] > 0):
    foreach ($_from as $this->_tpl_vars['option']):
        $this->_foreach['cb']['iteration']++;
?>
              <?php if (( ! isset ( $this->_tpl_vars['option']['active_option'] ) || $this->_tpl_vars['option']['active_option'] == 1 || is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] ) )): ?>
                <input type="checkbox" name="<?php echo $this->_tpl_vars['name']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>[<?php echo $this->_tpl_vars['index_array']; ?>
]<?php endif; ?>[]" id="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['cb']['iteration']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" value="<?php echo ((is_array($_tmp=$this->_tpl_vars['option']['option_value'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
" title="<?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('strip_tags', true, $_tmp, false) : smarty_modifier_strip_tags($_tmp, false)))) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)); ?>
"<?php if (is_array ( $this->_tpl_vars['value'] ) && in_array ( $this->_tpl_vars['option']['option_value'] , $this->_tpl_vars['value'] )): ?> checked="checked"<?php endif; ?><?php if ($this->_tpl_vars['disabled']): ?> disabled="disabled"<?php endif; ?><?php if ($this->_tpl_vars['readonly']): ?> onclick="deselectCheckboxes(this)"<?php endif; ?><?php if ($this->_tpl_vars['custom_class'] || $this->_tpl_vars['option']['class_name']): ?> class="<?php echo $this->_tpl_vars['custom_class']; ?>
 <?php echo $this->_tpl_vars['option']['class_name']; ?>
"<?php endif; ?> /><label for="<?php echo ((is_array($_tmp=@$this->_tpl_vars['custom_id'])) ? $this->_run_mod_handler('default', true, $_tmp, @$this->_tpl_vars['name']) : smarty_modifier_default($_tmp, @$this->_tpl_vars['name'])); ?>
_<?php echo $this->_foreach['cb']['iteration']; ?>
<?php if ($this->_tpl_vars['group_index']): ?>_<?php echo $this->_tpl_vars['index']; ?>
<?php endif; ?>" class="<?php if ($this->_tpl_vars['option']['class_name']): ?><?php echo $this->_tpl_vars['option']['class_name']; ?>
 <?php endif; ?><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>inactive_option<?php endif; ?>"<?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?> title="<?php echo $this->_config[0]['vars']['inactive_option']; ?>
"<?php endif; ?>><?php if (( isset ( $this->_tpl_vars['option']['active_option'] ) && $this->_tpl_vars['option']['active_option'] == 0 && ! $this->_tpl_vars['disable_inactive_style'] )): ?>* <?php endif; ?><?php if ($this->_tpl_vars['do_not_escape_labels']): ?><?php echo ((is_array($_tmp=@$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php else: ?><?php echo ((is_array($_tmp=((is_array($_tmp=$this->_tpl_vars['option']['label'])) ? $this->_run_mod_handler('escape', true, $_tmp) : smarty_modifier_escape($_tmp)))) ? $this->_run_mod_handler('default', true, $_tmp, '&nbsp;') : smarty_modifier_default($_tmp, '&nbsp;')); ?>
<?php endif; ?></label><?php if ($this->_tpl_vars['options_align'] != 'horizontal' && ! ($this->_foreach['cb']['iteration'] == $this->_foreach['cb']['total'])): ?><br /><?php endif; ?>
              <?php endif; ?>
            <?php endforeach; endif; unset($_from); ?>
        <?php if (! $this->_tpl_vars['standalone']): ?>
            </td>
          </tr>
        </table>
        <?php endif; ?>
      <?php if ($this->_tpl_vars['options_count'] > 10): ?></div><?php endif; ?>
    <?php endif; ?>

        <?php if (! $this->_tpl_vars['back_label'] && $this->_tpl_vars['var']['back_label']): ?>
      <?php $this->assign('back_label', $this->_tpl_vars['var']['back_label']); ?>
    <?php endif; ?>
    <?php if (! $this->_tpl_vars['back_label_style'] && $this->_tpl_vars['var']['back_label_style']): ?>
      <?php $this->assign('back_label_style', $this->_tpl_vars['var']['back_label_style']); ?>
    <?php endif; ?>
        <?php $_smarty_tpl_vars = $this->_tpl_vars;
$this->_smarty_include(array('smarty_include_tpl_file' => "_back_label.html", 'smarty_include_vars' => array('custom_id' => '','name' => '','index' => '','back_label' => $this->_tpl_vars['back_label'],'back_label_style' => $this->_tpl_vars['back_label_style'])));
$this->_tpl_vars = $_smarty_tpl_vars;
unset($_smarty_tpl_vars);
 ?>
  </div>

<?php if (! $this->_tpl_vars['standalone']): ?>
  </td>
</tr>
<?php endif; ?>